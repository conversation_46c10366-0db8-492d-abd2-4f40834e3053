/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
var SM;
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./index.js":
/*!******************!*\
  !*** ./index.js ***!
  \******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

        eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"decrypt\": () => (/* reexport safe */ _src_encrypt__WEBPACK_IMPORTED_MODULE_0__.decrypt),\n/* harmony export */   \"encrypt\": () => (/* reexport safe */ _src_encrypt__WEBPACK_IMPORTED_MODULE_0__.encrypt)\n/* harmony export */ });\n/* harmony import */ var _src_encrypt__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/encrypt */ \"./src/encrypt.js\");\n\r\n\n\n//# sourceURL=webpack://SM/./index.js?");

        /***/
}),

/***/ "./src/encrypt.js":
/*!************************!*\
  !*** ./src/encrypt.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

        eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"decrypt\": () => (/* binding */ decrypt),\n/* harmony export */   \"encrypt\": () => (/* binding */ encrypt)\n/* harmony export */ });\n/* harmony import */ var _sm2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sm2 */ \"./src/sm2/index.js\");\n\r\n\r\nfunction encrypt(str, pubk) {\r\n  return '04' + _sm2__WEBPACK_IMPORTED_MODULE_0__[\"default\"].doEncrypt(str, pubk, 1)\r\n}\r\nfunction decrypt(str, pri) {\r\n  str = str.substring(2)\r\n  return _sm2__WEBPACK_IMPORTED_MODULE_0__[\"default\"].doDecrypt(str, pri, 1)\r\n}\r\n\r\n\r\n\n\n//# sourceURL=webpack://SM/./src/encrypt.js?");

        /***/
}),

/***/ "./src/sm2/asn1.js":
/*!*************************!*\
  !*** ./src/sm2/asn1.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

        eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"decodeDer\": () => (/* binding */ decodeDer),\n/* harmony export */   \"encodeDer\": () => (/* binding */ encodeDer)\n/* harmony export */ });\n/* harmony import */ var _jsbn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./jsbn.js */ \"./src/sm2/jsbn.js\");\n/* eslint-disable eqeqeq */\r\n\r\n\r\nfunction bigIntToMinTwosComplementsHex (bigIntegerValue) {\r\n  let h = bigIntegerValue.toString(16)\r\n  if (h.substr(0, 1) != '-') {\r\n    if (h.length % 2 == 1) {\r\n      h = '0' + h\r\n    } else if (!h.match(/^[0-7]/)) {\r\n      h = '00' + h\r\n    }\r\n  } else {\r\n    const hPos = h.substr(1)\r\n    let xorLen = hPos.length\r\n    if (xorLen % 2 == 1) {\r\n      xorLen += 1\r\n    } else if (!h.match(/^[0-7]/)) {\r\n      xorLen += 2\r\n    }\r\n    let hMask = ''\r\n    for (let i = 0; i < xorLen; i++) {\r\n      hMask += 'f'\r\n    }\r\n    const biMask = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(hMask, 16)\r\n    const biNeg = biMask.xor(bigIntegerValue).add(_jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE)\r\n    h = biNeg.toString(16).replace(/^-/, '')\r\n  }\r\n  return h\r\n}\r\n\r\n/**\r\n * base class for ASN.1 DER encoder object\r\n */\r\nclass ASN1Object {\r\n  constructor () {\r\n    this.isModified = true\r\n    this.hTLV = null\r\n    this.hT = '00'\r\n    this.hL = '00'\r\n    this.hV = ''\r\n  }\r\n\r\n  /**\r\n     * get hexadecimal ASN.1 TLV length(L) bytes from TLV value(V)\r\n     */\r\n  getLengthHexFromValue () {\r\n    const n = this.hV.length / 2\r\n    let hN = n.toString(16)\r\n    if (hN.length % 2 == 1) {\r\n      hN = '0' + hN\r\n    }\r\n    if (n < 128) {\r\n      return hN\r\n    } else {\r\n      const hNlen = hN.length / 2\r\n      const head = 128 + hNlen\r\n      return head.toString(16) + hN\r\n    }\r\n  }\r\n\r\n  /**\r\n     * get hexadecimal string of ASN.1 TLV bytes\r\n     */\r\n  getEncodedHex () {\r\n    if (this.hTLV == null || this.isModified) {\r\n      this.hV = this.getFreshValueHex()\r\n      this.hL = this.getLengthHexFromValue()\r\n      this.hTLV = this.hT + this.hL + this.hV\r\n      this.isModified = false\r\n    }\r\n    return this.hTLV\r\n  }\r\n\r\n  getFreshValueHex () {\r\n    return ''\r\n  }\r\n};\r\n\r\n/**\r\n * class for ASN.1 DER Integer\r\n */\r\nclass DERInteger extends ASN1Object {\r\n  constructor (options) {\r\n    super()\r\n\r\n    this.hT = '02'\r\n    if (options && options.bigint) {\r\n      this.hTLV = null\r\n      this.isModified = true\r\n      this.hV = bigIntToMinTwosComplementsHex(options.bigint)\r\n    }\r\n  }\r\n\r\n  getFreshValueHex () {\r\n    return this.hV\r\n  }\r\n}\r\n\r\n/**\r\n * class for ASN.1 DER Sequence\r\n */\r\nclass DERSequence extends ASN1Object {\r\n  constructor (options) {\r\n    super()\r\n\r\n    this.hT = '30'\r\n    this.asn1Array = []\r\n    if (options && options.array) {\r\n      this.asn1Array = options.array\r\n    }\r\n  }\r\n\r\n  getFreshValueHex () {\r\n    let h = ''\r\n    for (let i = 0; i < this.asn1Array.length; i++) {\r\n      const asn1Obj = this.asn1Array[i]\r\n      h += asn1Obj.getEncodedHex()\r\n    }\r\n    this.hV = h\r\n    return this.hV\r\n  }\r\n}\r\n\r\n/**\r\n * get byte length for ASN.1 L(length) bytes\r\n */\r\nfunction getByteLengthOfL (s, pos) {\r\n  if (s.substring(pos + 2, pos + 3) != '8') return 1\r\n  const i = parseInt(s.substring(pos + 3, pos + 4))\r\n  if (i == 0) return -1 // length octet '80' indefinite length\r\n  if (i > 0 && i < 10) return i + 1 // including '8?' octet;\r\n  return -2 // malformed format\r\n}\r\n\r\n/**\r\n * get hexadecimal string for ASN.1 L(length) bytes\r\n */\r\nfunction getHexOfL (s, pos) {\r\n  const len = getByteLengthOfL(s, pos)\r\n  if (len < 1) return ''\r\n  return s.substring(pos + 2, pos + 2 + len * 2)\r\n}\r\n\r\n/**\r\n * get integer value of ASN.1 length for ASN.1 data\r\n */\r\nfunction getIntOfL (s, pos) {\r\n  const hLength = getHexOfL(s, pos)\r\n  if (hLength == '') return -1\r\n  let bi\r\n  if (parseInt(hLength.substring(0, 1)) < 8) {\r\n    bi = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(hLength, 16)\r\n  } else {\r\n    bi = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(hLength.substring(2), 16)\r\n  }\r\n  return bi.intValue()\r\n}\r\n\r\n/**\r\n * get ASN.1 value starting string position for ASN.1 object refered by index 'idx'.\r\n */\r\nfunction getStartPosOfV (s, pos) {\r\n  const lLen = getByteLengthOfL(s, pos)\r\n  if (lLen < 0) return lLen\r\n  return pos + (lLen + 1) * 2\r\n}\r\n\r\n/**\r\n * get hexadecimal string of ASN.1 V(value)\r\n */\r\nfunction getHexOfV (s, pos) {\r\n  const pos1 = getStartPosOfV(s, pos)\r\n  const len = getIntOfL(s, pos)\r\n  return s.substring(pos1, pos1 + len * 2)\r\n}\r\n\r\n/**\r\n * get next sibling starting index for ASN.1 object string\r\n */\r\nfunction getPosOfNextSibling (s, pos) {\r\n  const pos1 = getStartPosOfV(s, pos)\r\n  const len = getIntOfL(s, pos)\r\n  return pos1 + len * 2\r\n}\r\n\r\n/**\r\n * get array of indexes of child ASN.1 objects\r\n */\r\nfunction getPosArrayOfChildren (h, pos) {\r\n  const a = []\r\n  const p0 = getStartPosOfV(h, pos)\r\n  a.push(p0)\r\n\r\n  const len = getIntOfL(h, pos)\r\n  let p = p0\r\n  let k = 0\r\n  while (1) {\r\n    var pNext = getPosOfNextSibling(h, p)\r\n    if (pNext == null || (pNext - p0 >= (len * 2))) break\r\n    if (k >= 200) break\r\n\r\n    a.push(pNext)\r\n    p = pNext\r\n\r\n    k++\r\n  }\r\n\r\n  return a\r\n}\r\n\r\n/**\r\n   * ASN.1 DER编码\r\n   */\r\nfunction encodeDer (r, s) {\r\n  const derR = new DERInteger({ bigint: r })\r\n  const derS = new DERInteger({ bigint: s })\r\n  const derSeq = new DERSequence({ array: [derR, derS] })\r\n\r\n  return derSeq.getEncodedHex()\r\n}\r\n\r\n/**\r\n   * 解析 ASN.1 DER\r\n   */\r\nfunction decodeDer (input) {\r\n  // 1. Items of ASN.1 Sequence Check\r\n  const a = getPosArrayOfChildren(input, 0)\r\n\r\n  // 2. Integer check\r\n  const iTLV1 = a[0]\r\n  const iTLV2 = a[1]\r\n\r\n  // 3. getting value\r\n  const hR = getHexOfV(input, iTLV1)\r\n  const hS = getHexOfV(input, iTLV2)\r\n\r\n  const r = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(hR, 16)\r\n  const s = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(hS, 16)\r\n\r\n  return { r, s }\r\n}\r\n\r\n\r\n\n\n//# sourceURL=webpack://SM/./src/sm2/asn1.js?");

        /***/
}),

/***/ "./src/sm2/ec.js":
/*!***********************!*\
  !*** ./src/sm2/ec.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

        eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"ECCurveFp\": () => (/* binding */ ECCurveFp),\n/* harmony export */   \"ECPointFp\": () => (/* binding */ ECPointFp)\n/* harmony export */ });\n/* harmony import */ var _jsbn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./jsbn.js */ \"./src/sm2/jsbn.js\");\n/* eslint-disable eqeqeq */\r\n\r\n\r\n/**\r\n * thanks for Tom Wu : http://www-cs-students.stanford.edu/~tjw/jsbn/\r\n *\r\n * Basic Javascript Elliptic Curve implementation\r\n * Ported loosely from BouncyCastle's Java EC code\r\n * Only Fp curves implemented for now\r\n */\r\n\r\nconst THREE = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger('3')\r\n\r\n/**\r\n * 椭圆曲线域元素\r\n */\r\nclass ECFieldElementFp {\r\n  constructor (q, x) {\r\n    this.x = x\r\n    this.q = q\r\n  }\r\n\r\n  /**\r\n     * 判断相等\r\n     */\r\n  equals (other) {\r\n    if (other == this) return true\r\n    return (this.q.equals(other.q) && this.x.equals(other.x))\r\n  }\r\n\r\n  /**\r\n     * 返回具体数值\r\n     */\r\n  toBigInteger () {\r\n    return this.x\r\n  }\r\n\r\n  /**\r\n     * 取反\r\n     */\r\n  negate () {\r\n    return new ECFieldElementFp(this.q, this.x.negate().mod(this.q))\r\n  }\r\n\r\n  /**\r\n     * 相加\r\n     */\r\n  add (b) {\r\n    return new ECFieldElementFp(this.q, this.x.add(b.toBigInteger()).mod(this.q))\r\n  }\r\n\r\n  /**\r\n     * 相减\r\n     */\r\n  subtract (b) {\r\n    return new ECFieldElementFp(this.q, this.x.subtract(b.toBigInteger()).mod(this.q))\r\n  }\r\n\r\n  /**\r\n     * 相乘\r\n     */\r\n  multiply (b) {\r\n    return new ECFieldElementFp(this.q, this.x.multiply(b.toBigInteger()).mod(this.q))\r\n  }\r\n\r\n  /**\r\n     * 相除\r\n     */\r\n  divide (b) {\r\n    return new ECFieldElementFp(this.q, this.x.multiply(b.toBigInteger().modInverse(this.q)).mod(this.q))\r\n  }\r\n\r\n  /**\r\n     * 平方\r\n     */\r\n  square () {\r\n    return new ECFieldElementFp(this.q, this.x.square().mod(this.q))\r\n  }\r\n}\r\n\r\nclass ECPointFp {\r\n  constructor (curve, x, y, z) {\r\n    this.curve = curve\r\n    this.x = x\r\n    this.y = y\r\n    // 标准射影坐标系：zinv == null 或 z * zinv == 1\r\n    this.z = z == undefined ? _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE : z\r\n    this.zinv = null\r\n    // TODO: compression flag\r\n  }\r\n\r\n  getX () {\r\n    if (this.zinv == null) this.zinv = this.z.modInverse(this.curve.q)\r\n\r\n    return this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))\r\n  }\r\n\r\n  getY () {\r\n    if (this.zinv == null) this.zinv = this.z.modInverse(this.curve.q)\r\n\r\n    return this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))\r\n  }\r\n\r\n  /**\r\n     * 判断相等\r\n     */\r\n  equals (other) {\r\n    if (other == this) return true\r\n    if (this.isInfinity()) return other.isInfinity()\r\n    if (other.isInfinity()) return this.isInfinity()\r\n\r\n    // u = y2 * z1 - y1 * z2\r\n    const u = other.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(other.z)).mod(this.curve.q)\r\n    if (!u.equals(_jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ZERO)) return false\r\n\r\n    // v = x2 * z1 - x1 * z2\r\n    const v = other.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(other.z)).mod(this.curve.q)\r\n    return v.equals(_jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ZERO)\r\n  }\r\n\r\n  /**\r\n     * 是否是无穷远点\r\n     */\r\n  isInfinity () {\r\n    if ((this.x == null) && (this.y == null)) return true\r\n    return this.z.equals(_jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ZERO) && !this.y.toBigInteger().equals(_jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ZERO)\r\n  }\r\n\r\n  /**\r\n     * 取反，x 轴对称点\r\n     */\r\n  negate () {\r\n    return new ECPointFp(this.curve, this.x, this.y.negate(), this.z)\r\n  }\r\n\r\n  /**\r\n     * 相加\r\n     *\r\n     * 标准射影坐标系：\r\n     *\r\n     * λ1 = x1 * z2\r\n     * λ2 = x2 * z1\r\n     * λ3 = λ1 − λ2\r\n     * λ4 = y1 * z2\r\n     * λ5 = y2 * z1\r\n     * λ6 = λ4 − λ5\r\n     * λ7 = λ1 + λ2\r\n     * λ8 = z1 * z2\r\n     * λ9 = λ3^2\r\n     * λ10 = λ3 * λ9\r\n     * λ11 = λ8 * λ6^2 − λ7 * λ9\r\n     * x3 = λ3 * λ11\r\n     * y3 = λ6 * (λ9 * λ1 − λ11) − λ4 * λ10\r\n     * z3 = λ10 * λ8\r\n     */\r\n  add (b) {\r\n    if (this.isInfinity()) return b\r\n    if (b.isInfinity()) return this\r\n\r\n    const x1 = this.x.toBigInteger()\r\n    const y1 = this.y.toBigInteger()\r\n    const z1 = this.z\r\n    const x2 = b.x.toBigInteger()\r\n    const y2 = b.y.toBigInteger()\r\n    const z2 = b.z\r\n    const q = this.curve.q\r\n\r\n    const w1 = x1.multiply(z2).mod(q)\r\n    const w2 = x2.multiply(z1).mod(q)\r\n    const w3 = w1.subtract(w2)\r\n    const w4 = y1.multiply(z2).mod(q)\r\n    const w5 = y2.multiply(z1).mod(q)\r\n    const w6 = w4.subtract(w5)\r\n\r\n    if (_jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ZERO.equals(w3)) {\r\n      if (_jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ZERO.equals(w6)) {\r\n        return this.twice() // this == b，计算自加\r\n      }\r\n      return this.curve.infinity // this == -b，则返回无穷远点\r\n    }\r\n\r\n    const w7 = w1.add(w2)\r\n    const w8 = z1.multiply(z2).mod(q)\r\n    const w9 = w3.square().mod(q)\r\n    const w10 = w3.multiply(w9).mod(q)\r\n    const w11 = w8.multiply(w6.square()).subtract(w7.multiply(w9)).mod(q)\r\n\r\n    const x3 = w3.multiply(w11).mod(q)\r\n    const y3 = w6.multiply(w9.multiply(w1).subtract(w11)).subtract(w4.multiply(w10)).mod(q)\r\n    const z3 = w10.multiply(w8).mod(q)\r\n\r\n    return new ECPointFp(this.curve, this.curve.fromBigInteger(x3), this.curve.fromBigInteger(y3), z3)\r\n  }\r\n\r\n  /**\r\n     * 自加\r\n     *\r\n     * 标准射影坐标系：\r\n     *\r\n     * λ1 = 3 * x1^2 + a * z1^2\r\n     * λ2 = 2 * y1 * z1\r\n     * λ3 = y1^2\r\n     * λ4 = λ3 * x1 * z1\r\n     * λ5 = λ2^2\r\n     * λ6 = λ1^2 − 8 * λ4\r\n     * x3 = λ2 * λ6\r\n     * y3 = λ1 * (4 * λ4 − λ6) − 2 * λ5 * λ3\r\n     * z3 = λ2 * λ5\r\n     */\r\n  twice () {\r\n    if (this.isInfinity()) return this\r\n    if (!this.y.toBigInteger().signum()) return this.curve.infinity\r\n\r\n    const x1 = this.x.toBigInteger()\r\n    const y1 = this.y.toBigInteger()\r\n    const z1 = this.z\r\n    const q = this.curve.q\r\n    const a = this.curve.a.toBigInteger()\r\n\r\n    const w1 = x1.square().multiply(THREE).add(a.multiply(z1.square())).mod(q)\r\n    const w2 = y1.shiftLeft(1).multiply(z1).mod(q)\r\n    const w3 = y1.square().mod(q)\r\n    const w4 = w3.multiply(x1).multiply(z1).mod(q)\r\n    const w5 = w2.square().mod(q)\r\n    const w6 = w1.square().subtract(w4.shiftLeft(3)).mod(q)\r\n\r\n    const x3 = w2.multiply(w6).mod(q)\r\n    const y3 = w1.multiply(w4.shiftLeft(2).subtract(w6)).subtract(w5.shiftLeft(1).multiply(w3)).mod(q)\r\n    const z3 = w2.multiply(w5).mod(q)\r\n\r\n    return new ECPointFp(this.curve, this.curve.fromBigInteger(x3), this.curve.fromBigInteger(y3), z3)\r\n  }\r\n\r\n  /**\r\n     * 倍点计算\r\n     */\r\n  multiply (k) {\r\n    if (this.isInfinity()) return this\r\n    if (!k.signum()) return this.curve.infinity\r\n\r\n    // 使用加减法\r\n    const k3 = k.multiply(THREE)\r\n    const neg = this.negate()\r\n    let Q = this\r\n\r\n    for (let i = k3.bitLength() - 2; i > 0; i--) {\r\n      Q = Q.twice()\r\n\r\n      const k3Bit = k3.testBit(i)\r\n      const kBit = k.testBit(i)\r\n\r\n      if (k3Bit != kBit) {\r\n        Q = Q.add(k3Bit ? this : neg)\r\n      }\r\n    }\r\n\r\n    return Q\r\n  }\r\n}\r\n\r\n/**\r\n * 椭圆曲线 y^2 = x^3 + ax + b\r\n */\r\nclass ECCurveFp {\r\n  constructor (q, a, b) {\r\n    this.q = q\r\n    this.a = this.fromBigInteger(a)\r\n    this.b = this.fromBigInteger(b)\r\n    this.infinity = new ECPointFp(this, null, null) // 无穷远点\r\n  }\r\n\r\n  /**\r\n     * 判断两个椭圆曲线是否相等\r\n     */\r\n  equals (other) {\r\n    if (other == this) return true\r\n    return (this.q.equals(other.q) && this.a.equals(other.a) && this.b.equals(other.b))\r\n  }\r\n\r\n  /**\r\n     * 生成椭圆曲线域元素\r\n     */\r\n  fromBigInteger (x) {\r\n    return new ECFieldElementFp(this.q, x)\r\n  }\r\n\r\n  /**\r\n     * 解析 16 进制串为椭圆曲线点\r\n     */\r\n  decodePointHex (s) {\r\n    switch (parseInt(s.substr(0, 2), 16)) {\r\n      // 第一个字节\r\n      case 0:\r\n        return this.infinity\r\n      case 2:\r\n      case 3:\r\n        // 不支持的压缩方式\r\n        return null\r\n      case 4:\r\n      case 6:\r\n      case 7:\r\n        const len = (s.length - 2) / 2\r\n        const xHex = s.substr(2, len)\r\n        const yHex = s.substr(len + 2, len)\r\n\r\n        return new ECPointFp(this, this.fromBigInteger(new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(xHex, 16)), this.fromBigInteger(new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(yHex, 16)))\r\n      default:\r\n        // 不支持\r\n        return null\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\n\n//# sourceURL=webpack://SM/./src/sm2/ec.js?");

        /***/
}),

/***/ "./src/sm2/index.js":
/*!**************************!*\
  !*** ./src/sm2/index.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

        eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _jsbn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./jsbn.js */ \"./src/sm2/jsbn.js\");\n/* harmony import */ var _asn1__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./asn1 */ \"./src/sm2/asn1.js\");\n/* harmony import */ var _sm2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sm2 */ \"./src/sm2/sm2.js\");\n/* harmony import */ var _sm3__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sm3 */ \"./src/sm2/sm3.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils */ \"./src/sm2/utils.js\");\n/* eslint-disable eqeqeq */\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst { G, curve, n } = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].generateEcparam()\r\nconst C1C2C3 = 0\r\n\r\n/**\r\n * 加密\r\n */\r\nfunction doEncrypt (msg, publicKey, cipherMode = 1) {\r\n  const cipher = new _sm2__WEBPACK_IMPORTED_MODULE_2__[\"default\"]()\r\n  msg = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].hexToArray(_utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].parseUtf8StringToHex(msg))\r\n\r\n  if (publicKey.length > 128) {\r\n    publicKey = publicKey.substr(publicKey.length - 128)\r\n  }\r\n  const xHex = publicKey.substr(0, 64)\r\n  const yHex = publicKey.substr(64)\r\n  publicKey = cipher.createPoint(xHex, yHex)\r\n\r\n  const c1 = cipher.initEncipher(publicKey)\r\n\r\n  cipher.encryptBlock(msg)\r\n  const c2 = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].arrayToHex(msg)\r\n\r\n  let c3 = new Array(32)\r\n  cipher.doFinal(c3)\r\n  c3 = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].arrayToHex(c3)\r\n\r\n  return cipherMode == C1C2C3 ? c1 + c2 + c3 : c1 + c3 + c2\r\n}\r\n\r\n/**\r\n * 解密\r\n */\r\nfunction doDecrypt (encryptData, privateKey, cipherMode = 1) {\r\n  const cipher = new _sm2__WEBPACK_IMPORTED_MODULE_2__[\"default\"]()\r\n\r\n  privateKey = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(privateKey, 16)\r\n\r\n  const c1X = encryptData.substr(0, 64)\r\n  const c1Y = encryptData.substr(0 + c1X.length, 64)\r\n  const c1Length = c1X.length + c1Y.length\r\n\r\n  let c3 = encryptData.substr(c1Length, 64)\r\n  let c2 = encryptData.substr(c1Length + 64)\r\n\r\n  if (cipherMode == C1C2C3) {\r\n    c3 = encryptData.substr(encryptData.length - 64)\r\n    c2 = encryptData.substr(c1Length, encryptData.length - c1Length - 64)\r\n  }\r\n\r\n  const data = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].hexToArray(c2)\r\n\r\n  const c1 = cipher.createPoint(c1X, c1Y)\r\n  cipher.initDecipher(privateKey, c1)\r\n  cipher.decryptBlock(data)\r\n  const c3_ = new Array(32)\r\n  cipher.doFinal(c3_)\r\n\r\n  const isDecrypt = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].arrayToHex(c3_) == c3\r\n\r\n  if (isDecrypt) {\r\n    const decryptData = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].arrayToUtf8(data)\r\n    return decryptData\r\n  } else {\r\n    return ''\r\n  }\r\n}\r\n\r\n/**\r\n * 签名\r\n */\r\nfunction doSignature (msg, privateKey, { pointPool, der, hash, publicKey } = {}) {\r\n  let hashHex = typeof msg === 'string' ? _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].parseUtf8StringToHex(msg) : _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].parseArrayBufferToHex(msg)\r\n\r\n  if (hash) {\r\n    // sm3杂凑\r\n    publicKey = publicKey || getPublicKeyFromPrivateKey(privateKey)\r\n    hashHex = doSm3Hash(hashHex, publicKey)\r\n  }\r\n\r\n  const dA = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(privateKey, 16)\r\n  const e = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(hashHex, 16)\r\n\r\n  // k\r\n  let k = null\r\n  let r = null\r\n  let s = null\r\n\r\n  do {\r\n    do {\r\n      let point\r\n      if (pointPool && pointPool.length) {\r\n        point = pointPool.pop()\r\n      } else {\r\n        point = getPoint()\r\n      }\r\n      k = point.k\r\n\r\n      // r = (e + x1) mod n\r\n      r = e.add(point.x1).mod(n)\r\n    } while (r.equals(_jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ZERO) || r.add(k).equals(n))\r\n\r\n    // s = ((1 + dA)^-1 * (k - r * dA)) mod n\r\n    s = dA.add(_jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE).modInverse(n).multiply(k.subtract(r.multiply(dA))).mod(n)\r\n  } while (s.equals(_jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ZERO))\r\n\r\n  if (der) {\r\n    // asn1 der编码\r\n    return (0,_asn1__WEBPACK_IMPORTED_MODULE_1__.encodeDer)(r, s)\r\n  }\r\n\r\n  return _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].leftPad(r.toString(16), 64) + _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].leftPad(s.toString(16), 64)\r\n}\r\n\r\n/**\r\n * 验签\r\n */\r\nfunction doVerifySignature (msg, signHex, publicKey, { der, hash } = {}) {\r\n  let hashHex = typeof msg === 'string' ? _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].parseUtf8StringToHex(msg) : _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].parseArrayBufferToHex(msg)\r\n\r\n  if (hash) {\r\n    // sm3杂凑\r\n    hashHex = doSm3Hash(hashHex, publicKey)\r\n  }\r\n\r\n  let r, s\r\n  if (der) {\r\n    const decodeDerObj = (0,_asn1__WEBPACK_IMPORTED_MODULE_1__.decodeDer)(signHex)\r\n    r = decodeDerObj.r\r\n    s = decodeDerObj.s\r\n  } else {\r\n    r = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(signHex.substring(0, 64), 16)\r\n    s = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(signHex.substring(64), 16)\r\n  }\r\n\r\n  const PA = curve.decodePointHex(publicKey)\r\n  const e = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(hashHex, 16)\r\n\r\n  // t = (r + s) mod n\r\n  const t = r.add(s).mod(n)\r\n\r\n  if (t.equals(_jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ZERO)) return false\r\n\r\n  // x1y1 = s * G + t * PA\r\n  const x1y1 = G.multiply(s).add(PA.multiply(t))\r\n\r\n  // R = (e + x1) mod n\r\n  const R = e.add(x1y1.getX().toBigInteger()).mod(n)\r\n\r\n  return r.equals(R)\r\n}\r\n\r\n/**\r\n * sm3杂凑算法\r\n */\r\nfunction doSm3Hash (hashHex, publicKey) {\r\n  const smDigest = new _sm3__WEBPACK_IMPORTED_MODULE_3__[\"default\"]()\r\n\r\n  const z = new _sm3__WEBPACK_IMPORTED_MODULE_3__[\"default\"]().getZ(G, publicKey.substr(2, 128))\r\n  const zValue = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].hexToArray(_utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].arrayToHex(z).toString())\r\n\r\n  const p = hashHex\r\n  const pValue = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].hexToArray(p)\r\n\r\n  const hashData = new Array(smDigest.getDigestSize())\r\n  smDigest.blockUpdate(zValue, 0, zValue.length)\r\n  smDigest.blockUpdate(pValue, 0, pValue.length)\r\n  smDigest.doFinal(hashData, 0)\r\n\r\n  return _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].arrayToHex(hashData).toString()\r\n}\r\n\r\n/**\r\n * 计算公钥\r\n */\r\nfunction getPublicKeyFromPrivateKey (privateKey) {\r\n  const PA = G.multiply(new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(privateKey, 16))\r\n  const x = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].leftPad(PA.getX().toBigInteger().toString(16), 64)\r\n  const y = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].leftPad(PA.getY().toBigInteger().toString(16), 64)\r\n  return '04' + x + y\r\n}\r\n\r\n/**\r\n * 获取椭圆曲线点\r\n */\r\nfunction getPoint () {\r\n  const keypair = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].generateKeyPairHex()\r\n  const PA = curve.decodePointHex(keypair.publicKey)\r\n\r\n  keypair.k = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(keypair.privateKey, 16)\r\n  keypair.x1 = PA.getX().toBigInteger()\r\n\r\n  return keypair\r\n};\r\n\r\nconst generateKeyPairHex = _utils__WEBPACK_IMPORTED_MODULE_4__[\"default\"].generateKeyPairHex\r\n\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\r\n  generateKeyPairHex,\r\n  doEncrypt,\r\n  doDecrypt,\r\n  doSignature,\r\n  doVerifySignature,\r\n  getPoint\r\n});\r\n\n\n//# sourceURL=webpack://SM/./src/sm2/index.js?");

        /***/
}),

/***/ "./src/sm2/jsbn.js":
/*!*************************!*\
  !*** ./src/sm2/jsbn.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

        eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"BigInteger\": () => (/* binding */ BigInteger),\n/* harmony export */   \"SecureRandom\": () => (/* binding */ SecureRandom),\n/* harmony export */   \"default\": () => (/* binding */ BigInteger)\n/* harmony export */ });\n/* eslint-disable eqeqeq */\r\n/* eslint-disable camelcase */\r\n// Copyright (c) 2005  Tom Wu\r\n// All Rights Reserved.\r\n// See \"LICENSE\" for details.\r\n\r\n// Basic JavaScript BN library - subset useful for RSA encryption.\r\n\r\n// Bits per digit\r\nvar dbits\r\n\r\n// JavaScript engine analysis\r\nvar canary = 0xdeadbeefcafe\r\nvar j_lm = ((canary & 0xffffff) == 0xefcafe)\r\n\r\n// (public) Constructor\r\nfunction BigInteger (a, b, c) {\r\n  if (a != null) {\r\n    if (typeof a === 'number') this.fromNumber(a, b, c)\r\n    else if (b == null && typeof a !== 'string') this.fromString(a, 256)\r\n    else this.fromString(a, b)\r\n  }\r\n}\r\n\r\n// return new, unset BigInteger\r\nfunction nbi () { return new BigInteger(null) }\r\n\r\n// am: Compute w_j += (x*this_i), propagate carries,\r\n// c is initial carry, returns final carry.\r\n// c < 3*dvalue, x < 2*dvalue, this_i < dvalue\r\n// We need to select the fastest one that works in this environment.\r\n\r\n// am1: use a single mult and divide to get the high bits,\r\n// max digit bits should be 26 because\r\n// max internal value = 2*dvalue^2-2*dvalue (< 2^53)\r\nfunction am1 (i, x, w, j, c, n) {\r\n  while (--n >= 0) {\r\n    var v = x * this[i++] + w[j] + c\r\n    c = Math.floor(v / 0x4000000)\r\n    w[j++] = v & 0x3ffffff\r\n  }\r\n  return c\r\n}\r\n// am2 avoids a big mult-and-extract completely.\r\n// Max digit bits should be <= 30 because we do bitwise ops\r\n// on values up to 2*hdvalue^2-hdvalue-1 (< 2^31)\r\nfunction am2 (i, x, w, j, c, n) {\r\n  var xl = x & 0x7fff; var xh = x >> 15\r\n  while (--n >= 0) {\r\n    var l = this[i] & 0x7fff\r\n    var h = this[i++] >> 15\r\n    var m = xh * l + h * xl\r\n    l = xl * l + ((m & 0x7fff) << 15) + w[j] + (c & 0x3fffffff)\r\n    c = (l >>> 30) + (m >>> 15) + xh * h + (c >>> 30)\r\n    w[j++] = l & 0x3fffffff\r\n  }\r\n  return c\r\n}\r\n// Alternately, set max digit bits to 28 since some\r\n// browsers slow down when dealing with 32-bit numbers.\r\nfunction am3 (i, x, w, j, c, n) {\r\n  var xl = x & 0x3fff; var xh = x >> 14\r\n  while (--n >= 0) {\r\n    var l = this[i] & 0x3fff\r\n    var h = this[i++] >> 14\r\n    var m = xh * l + h * xl\r\n    l = xl * l + ((m & 0x3fff) << 14) + w[j] + c\r\n    c = (l >> 28) + (m >> 14) + xh * h\r\n    w[j++] = l & 0xfffffff\r\n  }\r\n  return c\r\n}\r\nvar inBrowser = typeof navigator !== 'undefined'\r\nif (inBrowser && j_lm && (navigator.appName == 'Microsoft Internet Explorer')) {\r\n  BigInteger.prototype.am = am2\r\n  dbits = 30\r\n} else if (inBrowser && j_lm && (navigator.appName != 'Netscape')) {\r\n  BigInteger.prototype.am = am1\r\n  dbits = 26\r\n} else { // Mozilla/Netscape seems to prefer am3\r\n  BigInteger.prototype.am = am3\r\n  dbits = 28\r\n}\r\n\r\nBigInteger.prototype.DB = dbits\r\nBigInteger.prototype.DM = ((1 << dbits) - 1)\r\nBigInteger.prototype.DV = (1 << dbits)\r\n\r\nvar BI_FP = 52\r\nBigInteger.prototype.FV = Math.pow(2, BI_FP)\r\nBigInteger.prototype.F1 = BI_FP - dbits\r\nBigInteger.prototype.F2 = 2 * dbits - BI_FP\r\n\r\n// Digit conversions\r\nvar BI_RM = '0123456789abcdefghijklmnopqrstuvwxyz'\r\nvar BI_RC = []\r\nvar rr, vv\r\nrr = '0'.charCodeAt(0)\r\nfor (vv = 0; vv <= 9; ++vv) BI_RC[rr++] = vv\r\nrr = 'a'.charCodeAt(0)\r\nfor (vv = 10; vv < 36; ++vv) BI_RC[rr++] = vv\r\nrr = 'A'.charCodeAt(0)\r\nfor (vv = 10; vv < 36; ++vv) BI_RC[rr++] = vv\r\n\r\nfunction int2char (n) { return BI_RM.charAt(n) }\r\nfunction intAt (s, i) {\r\n  var c = BI_RC[s.charCodeAt(i)]\r\n  return (c == null) ? -1 : c\r\n}\r\n\r\n// (protected) copy this to r\r\nfunction bnpCopyTo (r) {\r\n  for (var i = this.t - 1; i >= 0; --i) r[i] = this[i]\r\n  r.t = this.t\r\n  r.s = this.s\r\n}\r\n\r\n// (protected) set from integer value x, -DV <= x < DV\r\nfunction bnpFromInt (x) {\r\n  this.t = 1\r\n  this.s = (x < 0) ? -1 : 0\r\n  if (x > 0) this[0] = x\r\n  else if (x < -1) this[0] = x + this.DV\r\n  else this.t = 0\r\n}\r\n\r\n// return bigint initialized to value\r\nfunction nbv (i) { var r = nbi(); r.fromInt(i); return r }\r\n\r\n// (protected) set from string and radix\r\nfunction bnpFromString (s, b) {\r\n  var k\r\n  if (b == 16) k = 4\r\n  else if (b == 8) k = 3\r\n  else if (b == 256) k = 8 // byte array\r\n  else if (b == 2) k = 1\r\n  else if (b == 32) k = 5\r\n  else if (b == 4) k = 2\r\n  else { this.fromRadix(s, b); return }\r\n  this.t = 0\r\n  this.s = 0\r\n  var i = s.length; var mi = false; var sh = 0\r\n  while (--i >= 0) {\r\n    var x = (k == 8) ? s[i] & 0xff : intAt(s, i)\r\n    if (x < 0) {\r\n      if (s.charAt(i) == '-') mi = true\r\n      continue\r\n    }\r\n    mi = false\r\n    if (sh == 0) { this[this.t++] = x } else if (sh + k > this.DB) {\r\n      this[this.t - 1] |= (x & ((1 << (this.DB - sh)) - 1)) << sh\r\n      this[this.t++] = (x >> (this.DB - sh))\r\n    } else { this[this.t - 1] |= x << sh }\r\n    sh += k\r\n    if (sh >= this.DB) sh -= this.DB\r\n  }\r\n  if (k == 8 && (s[0] & 0x80) != 0) {\r\n    this.s = -1\r\n    if (sh > 0) this[this.t - 1] |= ((1 << (this.DB - sh)) - 1) << sh\r\n  }\r\n  this.clamp()\r\n  if (mi) BigInteger.ZERO.subTo(this, this)\r\n}\r\n\r\n// (protected) clamp off excess high words\r\nfunction bnpClamp () {\r\n  var c = this.s & this.DM\r\n  while (this.t > 0 && this[this.t - 1] == c) --this.t\r\n}\r\n\r\n// (public) return string representation in given radix\r\nfunction bnToString (b) {\r\n  if (this.s < 0) return '-' + this.negate().toString(b)\r\n  var k\r\n  if (b == 16) k = 4\r\n  else if (b == 8) k = 3\r\n  else if (b == 2) k = 1\r\n  else if (b == 32) k = 5\r\n  else if (b == 4) k = 2\r\n  else return this.toRadix(b)\r\n  var km = (1 << k) - 1; var d; var m = false; var r = ''; var i = this.t\r\n  var p = this.DB - (i * this.DB) % k\r\n  if (i-- > 0) {\r\n    if (p < this.DB && (d = this[i] >> p) > 0) { m = true; r = int2char(d) }\r\n    while (i >= 0) {\r\n      if (p < k) {\r\n        d = (this[i] & ((1 << p) - 1)) << (k - p)\r\n        d |= this[--i] >> (p += this.DB - k)\r\n      } else {\r\n        d = (this[i] >> (p -= k)) & km\r\n        if (p <= 0) { p += this.DB; --i }\r\n      }\r\n      if (d > 0) m = true\r\n      if (m) r += int2char(d)\r\n    }\r\n  }\r\n  return m ? r : '0'\r\n}\r\n\r\n// (public) -this\r\nfunction bnNegate () { var r = nbi(); BigInteger.ZERO.subTo(this, r); return r }\r\n\r\n// (public) |this|\r\nfunction bnAbs () { return (this.s < 0) ? this.negate() : this }\r\n\r\n// (public) return + if this > a, - if this < a, 0 if equal\r\nfunction bnCompareTo (a) {\r\n  var r = this.s - a.s\r\n  if (r != 0) return r\r\n  var i = this.t\r\n  r = i - a.t\r\n  if (r != 0) return (this.s < 0) ? -r : r\r\n  while (--i >= 0) if ((r = this[i] - a[i]) != 0) return r\r\n  return 0\r\n}\r\n\r\n// returns bit length of the integer x\r\nfunction nbits (x) {\r\n  var r = 1; var t\r\n  if ((t = x >>> 16) != 0) { x = t; r += 16 }\r\n  if ((t = x >> 8) != 0) { x = t; r += 8 }\r\n  if ((t = x >> 4) != 0) { x = t; r += 4 }\r\n  if ((t = x >> 2) != 0) { x = t; r += 2 }\r\n  if ((t = x >> 1) != 0) { x = t; r += 1 }\r\n  return r\r\n}\r\n\r\n// (public) return the number of bits in \"this\"\r\nfunction bnBitLength () {\r\n  if (this.t <= 0) return 0\r\n  return this.DB * (this.t - 1) + nbits(this[this.t - 1] ^ (this.s & this.DM))\r\n}\r\n\r\n// (protected) r = this << n*DB\r\nfunction bnpDLShiftTo (n, r) {\r\n  var i\r\n  for (i = this.t - 1; i >= 0; --i) r[i + n] = this[i]\r\n  for (i = n - 1; i >= 0; --i) r[i] = 0\r\n  r.t = this.t + n\r\n  r.s = this.s\r\n}\r\n\r\n// (protected) r = this >> n*DB\r\nfunction bnpDRShiftTo (n, r) {\r\n  for (var i = n; i < this.t; ++i) r[i - n] = this[i]\r\n  r.t = Math.max(this.t - n, 0)\r\n  r.s = this.s\r\n}\r\n\r\n// (protected) r = this << n\r\nfunction bnpLShiftTo (n, r) {\r\n  var bs = n % this.DB\r\n  var cbs = this.DB - bs\r\n  var bm = (1 << cbs) - 1\r\n  var ds = Math.floor(n / this.DB); var c = (this.s << bs) & this.DM; var i\r\n  for (i = this.t - 1; i >= 0; --i) {\r\n    r[i + ds + 1] = (this[i] >> cbs) | c\r\n    c = (this[i] & bm) << bs\r\n  }\r\n  for (i = ds - 1; i >= 0; --i) r[i] = 0\r\n  r[ds] = c\r\n  r.t = this.t + ds + 1\r\n  r.s = this.s\r\n  r.clamp()\r\n}\r\n\r\n// (protected) r = this >> n\r\nfunction bnpRShiftTo (n, r) {\r\n  r.s = this.s\r\n  var ds = Math.floor(n / this.DB)\r\n  if (ds >= this.t) { r.t = 0; return }\r\n  var bs = n % this.DB\r\n  var cbs = this.DB - bs\r\n  var bm = (1 << bs) - 1\r\n  r[0] = this[ds] >> bs\r\n  for (var i = ds + 1; i < this.t; ++i) {\r\n    r[i - ds - 1] |= (this[i] & bm) << cbs\r\n    r[i - ds] = this[i] >> bs\r\n  }\r\n  if (bs > 0) r[this.t - ds - 1] |= (this.s & bm) << cbs\r\n  r.t = this.t - ds\r\n  r.clamp()\r\n}\r\n\r\n// (protected) r = this - a\r\nfunction bnpSubTo (a, r) {\r\n  var i = 0; var c = 0; var m = Math.min(a.t, this.t)\r\n  while (i < m) {\r\n    c += this[i] - a[i]\r\n    r[i++] = c & this.DM\r\n    c >>= this.DB\r\n  }\r\n  if (a.t < this.t) {\r\n    c -= a.s\r\n    while (i < this.t) {\r\n      c += this[i]\r\n      r[i++] = c & this.DM\r\n      c >>= this.DB\r\n    }\r\n    c += this.s\r\n  } else {\r\n    c += this.s\r\n    while (i < a.t) {\r\n      c -= a[i]\r\n      r[i++] = c & this.DM\r\n      c >>= this.DB\r\n    }\r\n    c -= a.s\r\n  }\r\n  r.s = (c < 0) ? -1 : 0\r\n  if (c < -1) r[i++] = this.DV + c\r\n  else if (c > 0) r[i++] = c\r\n  r.t = i\r\n  r.clamp()\r\n}\r\n\r\n// (protected) r = this * a, r != this,a (HAC 14.12)\r\n// \"this\" should be the larger one if appropriate.\r\nfunction bnpMultiplyTo (a, r) {\r\n  var x = this.abs(); var y = a.abs()\r\n  var i = x.t\r\n  r.t = i + y.t\r\n  while (--i >= 0) r[i] = 0\r\n  for (i = 0; i < y.t; ++i) r[i + x.t] = x.am(0, y[i], r, i, 0, x.t)\r\n  r.s = 0\r\n  r.clamp()\r\n  if (this.s != a.s) BigInteger.ZERO.subTo(r, r)\r\n}\r\n\r\n// (protected) r = this^2, r != this (HAC 14.16)\r\nfunction bnpSquareTo (r) {\r\n  var x = this.abs()\r\n  var i = r.t = 2 * x.t\r\n  while (--i >= 0) r[i] = 0\r\n  for (i = 0; i < x.t - 1; ++i) {\r\n    var c = x.am(i, x[i], r, 2 * i, 0, 1)\r\n    if ((r[i + x.t] += x.am(i + 1, 2 * x[i], r, 2 * i + 1, c, x.t - i - 1)) >= x.DV) {\r\n      r[i + x.t] -= x.DV\r\n      r[i + x.t + 1] = 1\r\n    }\r\n  }\r\n  if (r.t > 0) r[r.t - 1] += x.am(i, x[i], r, 2 * i, 0, 1)\r\n  r.s = 0\r\n  r.clamp()\r\n}\r\n\r\n// (protected) divide this by m, quotient and remainder to q, r (HAC 14.20)\r\n// r != q, this != m.  q or r may be null.\r\nfunction bnpDivRemTo (m, q, r) {\r\n  var pm = m.abs()\r\n  if (pm.t <= 0) return\r\n  var pt = this.abs()\r\n  if (pt.t < pm.t) {\r\n    if (q != null) q.fromInt(0)\r\n    if (r != null) this.copyTo(r)\r\n    return\r\n  }\r\n  if (r == null) r = nbi()\r\n  var y = nbi(); var ts = this.s; var ms = m.s\r\n  var nsh = this.DB - nbits(pm[pm.t - 1]) // normalize modulus\r\n  if (nsh > 0) { pm.lShiftTo(nsh, y); pt.lShiftTo(nsh, r) } else { pm.copyTo(y); pt.copyTo(r) }\r\n  var ys = y.t\r\n  var y0 = y[ys - 1]\r\n  if (y0 == 0) return\r\n  var yt = y0 * (1 << this.F1) + ((ys > 1) ? y[ys - 2] >> this.F2 : 0)\r\n  var d1 = this.FV / yt; var d2 = (1 << this.F1) / yt; var e = 1 << this.F2\r\n  var i = r.t; var j = i - ys; var t = (q == null) ? nbi() : q\r\n  y.dlShiftTo(j, t)\r\n  if (r.compareTo(t) >= 0) {\r\n    r[r.t++] = 1\r\n    r.subTo(t, r)\r\n  }\r\n  BigInteger.ONE.dlShiftTo(ys, t)\r\n  t.subTo(y, y) // \"negative\" y so we can replace sub with am later\r\n  while (y.t < ys) y[y.t++] = 0\r\n  while (--j >= 0) {\r\n    // Estimate quotient digit\r\n    var qd = (r[--i] == y0) ? this.DM : Math.floor(r[i] * d1 + (r[i - 1] + e) * d2)\r\n    if ((r[i] += y.am(0, qd, r, j, 0, ys)) < qd) { // Try it out\r\n      y.dlShiftTo(j, t)\r\n      r.subTo(t, r)\r\n      while (r[i] < --qd) r.subTo(t, r)\r\n    }\r\n  }\r\n  if (q != null) {\r\n    r.drShiftTo(ys, q)\r\n    if (ts != ms) BigInteger.ZERO.subTo(q, q)\r\n  }\r\n  r.t = ys\r\n  r.clamp()\r\n  if (nsh > 0) r.rShiftTo(nsh, r) // Denormalize remainder\r\n  if (ts < 0) BigInteger.ZERO.subTo(r, r)\r\n}\r\n\r\n// (public) this mod a\r\nfunction bnMod (a) {\r\n  var r = nbi()\r\n  this.abs().divRemTo(a, null, r)\r\n  if (this.s < 0 && r.compareTo(BigInteger.ZERO) > 0) a.subTo(r, r)\r\n  return r\r\n}\r\n\r\n// Modular reduction using \"classic\" algorithm\r\nfunction Classic (m) { this.m = m }\r\nfunction cConvert (x) {\r\n  if (x.s < 0 || x.compareTo(this.m) >= 0) return x.mod(this.m)\r\n  else return x\r\n}\r\nfunction cRevert (x) { return x }\r\nfunction cReduce (x) { x.divRemTo(this.m, null, x) }\r\nfunction cMulTo (x, y, r) { x.multiplyTo(y, r); this.reduce(r) }\r\nfunction cSqrTo (x, r) { x.squareTo(r); this.reduce(r) }\r\n\r\nClassic.prototype.convert = cConvert\r\nClassic.prototype.revert = cRevert\r\nClassic.prototype.reduce = cReduce\r\nClassic.prototype.mulTo = cMulTo\r\nClassic.prototype.sqrTo = cSqrTo\r\n\r\n// (protected) return \"-1/this % 2^DB\"; useful for Mont. reduction\r\n// justification:\r\n//         xy == 1 (mod m)\r\n//         xy =  1+km\r\n//   xy(2-xy) = (1+km)(1-km)\r\n// x[y(2-xy)] = 1-k^2m^2\r\n// x[y(2-xy)] == 1 (mod m^2)\r\n// if y is 1/x mod m, then y(2-xy) is 1/x mod m^2\r\n// should reduce x and y(2-xy) by m^2 at each step to keep size bounded.\r\n// JS multiply \"overflows\" differently from C/C++, so care is needed here.\r\nfunction bnpInvDigit () {\r\n  if (this.t < 1) return 0\r\n  var x = this[0]\r\n  if ((x & 1) == 0) return 0\r\n  var y = x & 3 // y == 1/x mod 2^2\r\n  y = (y * (2 - (x & 0xf) * y)) & 0xf // y == 1/x mod 2^4\r\n  y = (y * (2 - (x & 0xff) * y)) & 0xff // y == 1/x mod 2^8\r\n  y = (y * (2 - (((x & 0xffff) * y) & 0xffff))) & 0xffff // y == 1/x mod 2^16\r\n  // last step - calculate inverse mod DV directly;\r\n  // assumes 16 < DB <= 32 and assumes ability to handle 48-bit ints\r\n  y = (y * (2 - x * y % this.DV)) % this.DV // y == 1/x mod 2^dbits\r\n  // we really want the negative inverse, and -DV < y < DV\r\n  return (y > 0) ? this.DV - y : -y\r\n}\r\n\r\n// Montgomery reduction\r\nfunction Montgomery (m) {\r\n  this.m = m\r\n  this.mp = m.invDigit()\r\n  this.mpl = this.mp & 0x7fff\r\n  this.mph = this.mp >> 15\r\n  this.um = (1 << (m.DB - 15)) - 1\r\n  this.mt2 = 2 * m.t\r\n}\r\n\r\n// xR mod m\r\nfunction montConvert (x) {\r\n  var r = nbi()\r\n  x.abs().dlShiftTo(this.m.t, r)\r\n  r.divRemTo(this.m, null, r)\r\n  if (x.s < 0 && r.compareTo(BigInteger.ZERO) > 0) this.m.subTo(r, r)\r\n  return r\r\n}\r\n\r\n// x/R mod m\r\nfunction montRevert (x) {\r\n  var r = nbi()\r\n  x.copyTo(r)\r\n  this.reduce(r)\r\n  return r\r\n}\r\n\r\n// x = x/R mod m (HAC 14.32)\r\nfunction montReduce (x) {\r\n  while (x.t <= this.mt2) { // pad x so am has enough room later\r\n    x[x.t++] = 0\r\n  }\r\n  for (var i = 0; i < this.m.t; ++i) {\r\n    // faster way of calculating u0 = x[i]*mp mod DV\r\n    var j = x[i] & 0x7fff\r\n    var u0 = (j * this.mpl + (((j * this.mph + (x[i] >> 15) * this.mpl) & this.um) << 15)) & x.DM\r\n    // use am to combine the multiply-shift-add into one call\r\n    j = i + this.m.t\r\n    x[j] += this.m.am(0, u0, x, i, 0, this.m.t)\r\n    // propagate carry\r\n    while (x[j] >= x.DV) { x[j] -= x.DV; x[++j]++ }\r\n  }\r\n  x.clamp()\r\n  x.drShiftTo(this.m.t, x)\r\n  if (x.compareTo(this.m) >= 0) x.subTo(this.m, x)\r\n}\r\n\r\n// r = \"x^2/R mod m\"; x != r\r\nfunction montSqrTo (x, r) { x.squareTo(r); this.reduce(r) }\r\n\r\n// r = \"xy/R mod m\"; x,y != r\r\nfunction montMulTo (x, y, r) { x.multiplyTo(y, r); this.reduce(r) }\r\n\r\nMontgomery.prototype.convert = montConvert\r\nMontgomery.prototype.revert = montRevert\r\nMontgomery.prototype.reduce = montReduce\r\nMontgomery.prototype.mulTo = montMulTo\r\nMontgomery.prototype.sqrTo = montSqrTo\r\n\r\n// (protected) true iff this is even\r\nfunction bnpIsEven () { return ((this.t > 0) ? (this[0] & 1) : this.s) == 0 }\r\n\r\n// (protected) this^e, e < 2^32, doing sqr and mul with \"r\" (HAC 14.79)\r\nfunction bnpExp (e, z) {\r\n  if (e > 0xffffffff || e < 1) return BigInteger.ONE\r\n  var r = nbi(); var r2 = nbi(); var g = z.convert(this); var i = nbits(e) - 1\r\n  g.copyTo(r)\r\n  while (--i >= 0) {\r\n    z.sqrTo(r, r2)\r\n    if ((e & (1 << i)) > 0) z.mulTo(r2, g, r)\r\n    else { var t = r; r = r2; r2 = t }\r\n  }\r\n  return z.revert(r)\r\n}\r\n\r\n// (public) this^e % m, 0 <= e < 2^32\r\nfunction bnModPowInt (e, m) {\r\n  var z\r\n  if (e < 256 || m.isEven()) z = new Classic(m); else z = new Montgomery(m)\r\n  return this.exp(e, z)\r\n}\r\n\r\n// protected\r\nBigInteger.prototype.copyTo = bnpCopyTo\r\nBigInteger.prototype.fromInt = bnpFromInt\r\nBigInteger.prototype.fromString = bnpFromString\r\nBigInteger.prototype.clamp = bnpClamp\r\nBigInteger.prototype.dlShiftTo = bnpDLShiftTo\r\nBigInteger.prototype.drShiftTo = bnpDRShiftTo\r\nBigInteger.prototype.lShiftTo = bnpLShiftTo\r\nBigInteger.prototype.rShiftTo = bnpRShiftTo\r\nBigInteger.prototype.subTo = bnpSubTo\r\nBigInteger.prototype.multiplyTo = bnpMultiplyTo\r\nBigInteger.prototype.squareTo = bnpSquareTo\r\nBigInteger.prototype.divRemTo = bnpDivRemTo\r\nBigInteger.prototype.invDigit = bnpInvDigit\r\nBigInteger.prototype.isEven = bnpIsEven\r\nBigInteger.prototype.exp = bnpExp\r\n\r\n// public\r\nBigInteger.prototype.toString = bnToString\r\nBigInteger.prototype.negate = bnNegate\r\nBigInteger.prototype.abs = bnAbs\r\nBigInteger.prototype.compareTo = bnCompareTo\r\nBigInteger.prototype.bitLength = bnBitLength\r\nBigInteger.prototype.mod = bnMod\r\nBigInteger.prototype.modPowInt = bnModPowInt\r\n\r\n// \"constants\"\r\nBigInteger.ZERO = nbv(0)\r\nBigInteger.ONE = nbv(1)\r\n\r\n// Copyright (c) 2005-2009  Tom Wu\r\n// All Rights Reserved.\r\n// See \"LICENSE\" for details.\r\n\r\n// Extended JavaScript BN functions, required for RSA private ops.\r\n\r\n// Version 1.1: new BigInteger(\"0\", 10) returns \"proper\" zero\r\n// Version 1.2: square() API, isProbablePrime fix\r\n\r\n// (public)\r\nfunction bnClone () { var r = nbi(); this.copyTo(r); return r }\r\n\r\n// (public) return value as integer\r\nfunction bnIntValue () {\r\n  if (this.s < 0) {\r\n    if (this.t == 1) return this[0] - this.DV\r\n    else if (this.t == 0) return -1\r\n  } else if (this.t == 1) return this[0]\r\n  else if (this.t == 0) return 0\r\n  // assumes 16 < DB < 32\r\n  return ((this[1] & ((1 << (32 - this.DB)) - 1)) << this.DB) | this[0]\r\n}\r\n\r\n// (public) return value as byte\r\nfunction bnByteValue () { return (this.t == 0) ? this.s : (this[0] << 24) >> 24 }\r\n\r\n// (public) return value as short (assumes DB>=16)\r\nfunction bnShortValue () { return (this.t == 0) ? this.s : (this[0] << 16) >> 16 }\r\n\r\n// (protected) return x s.t. r^x < DV\r\nfunction bnpChunkSize (r) { return Math.floor(Math.LN2 * this.DB / Math.log(r)) }\r\n\r\n// (public) 0 if this == 0, 1 if this > 0\r\nfunction bnSigNum () {\r\n  if (this.s < 0) return -1\r\n  else if (this.t <= 0 || (this.t == 1 && this[0] <= 0)) return 0\r\n  else return 1\r\n}\r\n\r\n// (protected) convert to radix string\r\nfunction bnpToRadix (b) {\r\n  if (b == null) b = 10\r\n  if (this.signum() == 0 || b < 2 || b > 36) return '0'\r\n  var cs = this.chunkSize(b)\r\n  var a = Math.pow(b, cs)\r\n  var d = nbv(a); var y = nbi(); var z = nbi(); var r = ''\r\n  this.divRemTo(d, y, z)\r\n  while (y.signum() > 0) {\r\n    r = (a + z.intValue()).toString(b).substr(1) + r\r\n    y.divRemTo(d, y, z)\r\n  }\r\n  return z.intValue().toString(b) + r\r\n}\r\n\r\n// (protected) convert from radix string\r\nfunction bnpFromRadix (s, b) {\r\n  this.fromInt(0)\r\n  if (b == null) b = 10\r\n  var cs = this.chunkSize(b)\r\n  var d = Math.pow(b, cs); var mi = false; var j = 0; var w = 0\r\n  for (var i = 0; i < s.length; ++i) {\r\n    var x = intAt(s, i)\r\n    if (x < 0) {\r\n      if (s.charAt(i) == '-' && this.signum() == 0) mi = true\r\n      continue\r\n    }\r\n    w = b * w + x\r\n    if (++j >= cs) {\r\n      this.dMultiply(d)\r\n      this.dAddOffset(w, 0)\r\n      j = 0\r\n      w = 0\r\n    }\r\n  }\r\n  if (j > 0) {\r\n    this.dMultiply(Math.pow(b, j))\r\n    this.dAddOffset(w, 0)\r\n  }\r\n  if (mi) BigInteger.ZERO.subTo(this, this)\r\n}\r\n\r\n// (protected) alternate constructor\r\nfunction bnpFromNumber (a, b, c) {\r\n  if (typeof b === 'number') {\r\n    // new BigInteger(int,int,RNG)\r\n    if (a < 2) this.fromInt(1)\r\n    else {\r\n      this.fromNumber(a, c)\r\n      if (!this.testBit(a - 1)) { // force MSB set\r\n        this.bitwiseTo(BigInteger.ONE.shiftLeft(a - 1), op_or, this)\r\n      }\r\n      if (this.isEven()) this.dAddOffset(1, 0) // force odd\r\n      while (!this.isProbablePrime(b)) {\r\n        this.dAddOffset(2, 0)\r\n        if (this.bitLength() > a) this.subTo(BigInteger.ONE.shiftLeft(a - 1), this)\r\n      }\r\n    }\r\n  } else {\r\n    // new BigInteger(int,RNG)\r\n    var x = []\r\n    var t = a & 7\r\n    x.length = (a >> 3) + 1\r\n    b.nextBytes(x)\r\n    if (t > 0) x[0] &= ((1 << t) - 1); else x[0] = 0\r\n    this.fromString(x, 256)\r\n  }\r\n}\r\n\r\n// (public) convert to bigendian byte array\r\nfunction bnToByteArray () {\r\n  var i = this.t\r\n  var r = []\r\n  r[0] = this.s\r\n  var p = this.DB - (i * this.DB) % 8; var d; var k = 0\r\n  if (i-- > 0) {\r\n    if (p < this.DB && (d = this[i] >> p) != (this.s & this.DM) >> p) { r[k++] = d | (this.s << (this.DB - p)) }\r\n    while (i >= 0) {\r\n      if (p < 8) {\r\n        d = (this[i] & ((1 << p) - 1)) << (8 - p)\r\n        d |= this[--i] >> (p += this.DB - 8)\r\n      } else {\r\n        d = (this[i] >> (p -= 8)) & 0xff\r\n        if (p <= 0) { p += this.DB; --i }\r\n      }\r\n      if ((d & 0x80) != 0) d |= -256\r\n      if (k == 0 && (this.s & 0x80) != (d & 0x80)) ++k\r\n      if (k > 0 || d != this.s) r[k++] = d\r\n    }\r\n  }\r\n  return r\r\n}\r\n\r\nfunction bnEquals (a) { return (this.compareTo(a) == 0) }\r\nfunction bnMin (a) { return (this.compareTo(a) < 0) ? this : a }\r\nfunction bnMax (a) { return (this.compareTo(a) > 0) ? this : a }\r\n\r\n// (protected) r = this op a (bitwise)\r\nfunction bnpBitwiseTo (a, op, r) {\r\n  var i; var f; var m = Math.min(a.t, this.t)\r\n  for (i = 0; i < m; ++i) r[i] = op(this[i], a[i])\r\n  if (a.t < this.t) {\r\n    f = a.s & this.DM\r\n    for (i = m; i < this.t; ++i) r[i] = op(this[i], f)\r\n    r.t = this.t\r\n  } else {\r\n    f = this.s & this.DM\r\n    for (i = m; i < a.t; ++i) r[i] = op(f, a[i])\r\n    r.t = a.t\r\n  }\r\n  r.s = op(this.s, a.s)\r\n  r.clamp()\r\n}\r\n\r\n// (public) this & a\r\nfunction op_and (x, y) { return x & y }\r\nfunction bnAnd (a) { var r = nbi(); this.bitwiseTo(a, op_and, r); return r }\r\n\r\n// (public) this | a\r\nfunction op_or (x, y) { return x | y }\r\nfunction bnOr (a) { var r = nbi(); this.bitwiseTo(a, op_or, r); return r }\r\n\r\n// (public) this ^ a\r\nfunction op_xor (x, y) { return x ^ y }\r\nfunction bnXor (a) { var r = nbi(); this.bitwiseTo(a, op_xor, r); return r }\r\n\r\n// (public) this & ~a\r\nfunction op_andnot (x, y) { return x & ~y }\r\nfunction bnAndNot (a) { var r = nbi(); this.bitwiseTo(a, op_andnot, r); return r }\r\n\r\n// (public) ~this\r\nfunction bnNot () {\r\n  var r = nbi()\r\n  for (var i = 0; i < this.t; ++i) r[i] = this.DM & ~this[i]\r\n  r.t = this.t\r\n  r.s = ~this.s\r\n  return r\r\n}\r\n\r\n// (public) this << n\r\nfunction bnShiftLeft (n) {\r\n  var r = nbi()\r\n  if (n < 0) this.rShiftTo(-n, r); else this.lShiftTo(n, r)\r\n  return r\r\n}\r\n\r\n// (public) this >> n\r\nfunction bnShiftRight (n) {\r\n  var r = nbi()\r\n  if (n < 0) this.lShiftTo(-n, r); else this.rShiftTo(n, r)\r\n  return r\r\n}\r\n\r\n// return index of lowest 1-bit in x, x < 2^31\r\nfunction lbit (x) {\r\n  if (x == 0) return -1\r\n  var r = 0\r\n  if ((x & 0xffff) == 0) { x >>= 16; r += 16 }\r\n  if ((x & 0xff) == 0) { x >>= 8; r += 8 }\r\n  if ((x & 0xf) == 0) { x >>= 4; r += 4 }\r\n  if ((x & 3) == 0) { x >>= 2; r += 2 }\r\n  if ((x & 1) == 0) ++r\r\n  return r\r\n}\r\n\r\n// (public) returns index of lowest 1-bit (or -1 if none)\r\nfunction bnGetLowestSetBit () {\r\n  for (var i = 0; i < this.t; ++i) { if (this[i] != 0) return i * this.DB + lbit(this[i]) }\r\n  if (this.s < 0) return this.t * this.DB\r\n  return -1\r\n}\r\n\r\n// return number of 1 bits in x\r\nfunction cbit (x) {\r\n  var r = 0\r\n  while (x != 0) { x &= x - 1; ++r }\r\n  return r\r\n}\r\n\r\n// (public) return number of set bits\r\nfunction bnBitCount () {\r\n  var r = 0; var x = this.s & this.DM\r\n  for (var i = 0; i < this.t; ++i) r += cbit(this[i] ^ x)\r\n  return r\r\n}\r\n\r\n// (public) true iff nth bit is set\r\nfunction bnTestBit (n) {\r\n  var j = Math.floor(n / this.DB)\r\n  if (j >= this.t) return (this.s != 0)\r\n  return ((this[j] & (1 << (n % this.DB))) != 0)\r\n}\r\n\r\n// (protected) this op (1<<n)\r\nfunction bnpChangeBit (n, op) {\r\n  var r = BigInteger.ONE.shiftLeft(n)\r\n  this.bitwiseTo(r, op, r)\r\n  return r\r\n}\r\n\r\n// (public) this | (1<<n)\r\nfunction bnSetBit (n) { return this.changeBit(n, op_or) }\r\n\r\n// (public) this & ~(1<<n)\r\nfunction bnClearBit (n) { return this.changeBit(n, op_andnot) }\r\n\r\n// (public) this ^ (1<<n)\r\nfunction bnFlipBit (n) { return this.changeBit(n, op_xor) }\r\n\r\n// (protected) r = this + a\r\nfunction bnpAddTo (a, r) {\r\n  var i = 0; var c = 0; var m = Math.min(a.t, this.t)\r\n  while (i < m) {\r\n    c += this[i] + a[i]\r\n    r[i++] = c & this.DM\r\n    c >>= this.DB\r\n  }\r\n  if (a.t < this.t) {\r\n    c += a.s\r\n    while (i < this.t) {\r\n      c += this[i]\r\n      r[i++] = c & this.DM\r\n      c >>= this.DB\r\n    }\r\n    c += this.s\r\n  } else {\r\n    c += this.s\r\n    while (i < a.t) {\r\n      c += a[i]\r\n      r[i++] = c & this.DM\r\n      c >>= this.DB\r\n    }\r\n    c += a.s\r\n  }\r\n  r.s = (c < 0) ? -1 : 0\r\n  if (c > 0) r[i++] = c\r\n  else if (c < -1) r[i++] = this.DV + c\r\n  r.t = i\r\n  r.clamp()\r\n}\r\n\r\n// (public) this + a\r\nfunction bnAdd (a) { var r = nbi(); this.addTo(a, r); return r }\r\n\r\n// (public) this - a\r\nfunction bnSubtract (a) { var r = nbi(); this.subTo(a, r); return r }\r\n\r\n// (public) this * a\r\nfunction bnMultiply (a) { var r = nbi(); this.multiplyTo(a, r); return r }\r\n\r\n// (public) this^2\r\nfunction bnSquare () { var r = nbi(); this.squareTo(r); return r }\r\n\r\n// (public) this / a\r\nfunction bnDivide (a) { var r = nbi(); this.divRemTo(a, r, null); return r }\r\n\r\n// (public) this % a\r\nfunction bnRemainder (a) { var r = nbi(); this.divRemTo(a, null, r); return r }\r\n\r\n// (public) [this/a,this%a]\r\nfunction bnDivideAndRemainder (a) {\r\n  var q = nbi(); var r = nbi()\r\n  this.divRemTo(a, q, r)\r\n  return [q, r]\r\n}\r\n\r\n// (protected) this *= n, this >= 0, 1 < n < DV\r\nfunction bnpDMultiply (n) {\r\n  this[this.t] = this.am(0, n - 1, this, 0, 0, this.t)\r\n  ++this.t\r\n  this.clamp()\r\n}\r\n\r\n// (protected) this += n << w words, this >= 0\r\nfunction bnpDAddOffset (n, w) {\r\n  if (n == 0) return\r\n  while (this.t <= w) this[this.t++] = 0\r\n  this[w] += n\r\n  while (this[w] >= this.DV) {\r\n    this[w] -= this.DV\r\n    if (++w >= this.t) this[this.t++] = 0\r\n    ++this[w]\r\n  }\r\n}\r\n\r\n// A \"null\" reducer\r\nfunction NullExp () {}\r\nfunction nNop (x) { return x }\r\nfunction nMulTo (x, y, r) { x.multiplyTo(y, r) }\r\nfunction nSqrTo (x, r) { x.squareTo(r) }\r\n\r\nNullExp.prototype.convert = nNop\r\nNullExp.prototype.revert = nNop\r\nNullExp.prototype.mulTo = nMulTo\r\nNullExp.prototype.sqrTo = nSqrTo\r\n\r\n// (public) this^e\r\nfunction bnPow (e) { return this.exp(e, new NullExp()) }\r\n\r\n// (protected) r = lower n words of \"this * a\", a.t <= n\r\n// \"this\" should be the larger one if appropriate.\r\nfunction bnpMultiplyLowerTo (a, n, r) {\r\n  var i = Math.min(this.t + a.t, n)\r\n  r.s = 0 // assumes a,this >= 0\r\n  r.t = i\r\n  while (i > 0) r[--i] = 0\r\n  var j\r\n  for (j = r.t - this.t; i < j; ++i) r[i + this.t] = this.am(0, a[i], r, i, 0, this.t)\r\n  for (j = Math.min(a.t, n); i < j; ++i) this.am(0, a[i], r, i, 0, n - i)\r\n  r.clamp()\r\n}\r\n\r\n// (protected) r = \"this * a\" without lower n words, n > 0\r\n// \"this\" should be the larger one if appropriate.\r\nfunction bnpMultiplyUpperTo (a, n, r) {\r\n  --n\r\n  var i = r.t = this.t + a.t - n\r\n  r.s = 0 // assumes a,this >= 0\r\n  while (--i >= 0) r[i] = 0\r\n  for (i = Math.max(n - this.t, 0); i < a.t; ++i) { r[this.t + i - n] = this.am(n - i, a[i], r, 0, 0, this.t + i - n) }\r\n  r.clamp()\r\n  r.drShiftTo(1, r)\r\n}\r\n\r\n// Barrett modular reduction\r\nfunction Barrett (m) {\r\n  // setup Barrett\r\n  this.r2 = nbi()\r\n  this.q3 = nbi()\r\n  BigInteger.ONE.dlShiftTo(2 * m.t, this.r2)\r\n  this.mu = this.r2.divide(m)\r\n  this.m = m\r\n}\r\n\r\nfunction barrettConvert (x) {\r\n  if (x.s < 0 || x.t > 2 * this.m.t) return x.mod(this.m)\r\n  else if (x.compareTo(this.m) < 0) return x\r\n  else { var r = nbi(); x.copyTo(r); this.reduce(r); return r }\r\n}\r\n\r\nfunction barrettRevert (x) { return x }\r\n\r\n// x = x mod m (HAC 14.42)\r\nfunction barrettReduce (x) {\r\n  x.drShiftTo(this.m.t - 1, this.r2)\r\n  if (x.t > this.m.t + 1) { x.t = this.m.t + 1; x.clamp() }\r\n  this.mu.multiplyUpperTo(this.r2, this.m.t + 1, this.q3)\r\n  this.m.multiplyLowerTo(this.q3, this.m.t + 1, this.r2)\r\n  while (x.compareTo(this.r2) < 0) x.dAddOffset(1, this.m.t + 1)\r\n  x.subTo(this.r2, x)\r\n  while (x.compareTo(this.m) >= 0) x.subTo(this.m, x)\r\n}\r\n\r\n// r = x^2 mod m; x != r\r\nfunction barrettSqrTo (x, r) { x.squareTo(r); this.reduce(r) }\r\n\r\n// r = x*y mod m; x,y != r\r\nfunction barrettMulTo (x, y, r) { x.multiplyTo(y, r); this.reduce(r) }\r\n\r\nBarrett.prototype.convert = barrettConvert\r\nBarrett.prototype.revert = barrettRevert\r\nBarrett.prototype.reduce = barrettReduce\r\nBarrett.prototype.mulTo = barrettMulTo\r\nBarrett.prototype.sqrTo = barrettSqrTo\r\n\r\n// (public) this^e % m (HAC 14.85)\r\nfunction bnModPow (e, m) {\r\n  var i = e.bitLength(); var k; var r = nbv(1); var z\r\n  if (i <= 0) return r\r\n  else if (i < 18) k = 1\r\n  else if (i < 48) k = 3\r\n  else if (i < 144) k = 4\r\n  else if (i < 768) k = 5\r\n  else k = 6\r\n  if (i < 8) { z = new Classic(m) } else if (m.isEven()) { z = new Barrett(m) } else { z = new Montgomery(m) }\r\n\r\n  // precomputation\r\n  var g = []\r\n  var n = 3; var k1 = k - 1; var km = (1 << k) - 1\r\n  g[1] = z.convert(this)\r\n  if (k > 1) {\r\n    var g2 = nbi()\r\n    z.sqrTo(g[1], g2)\r\n    while (n <= km) {\r\n      g[n] = nbi()\r\n      z.mulTo(g2, g[n - 2], g[n])\r\n      n += 2\r\n    }\r\n  }\r\n\r\n  var j = e.t - 1; var w; var is1 = true; var r2 = nbi(); var t\r\n  i = nbits(e[j]) - 1\r\n  while (j >= 0) {\r\n    if (i >= k1) w = (e[j] >> (i - k1)) & km\r\n    else {\r\n      w = (e[j] & ((1 << (i + 1)) - 1)) << (k1 - i)\r\n      if (j > 0) w |= e[j - 1] >> (this.DB + i - k1)\r\n    }\r\n\r\n    n = k\r\n    while ((w & 1) == 0) { w >>= 1; --n }\r\n    if ((i -= n) < 0) { i += this.DB; --j }\r\n    if (is1) { // ret == 1, don't bother squaring or multiplying it\r\n      g[w].copyTo(r)\r\n      is1 = false\r\n    } else {\r\n      while (n > 1) { z.sqrTo(r, r2); z.sqrTo(r2, r); n -= 2 }\r\n      if (n > 0) z.sqrTo(r, r2); else { t = r; r = r2; r2 = t }\r\n      z.mulTo(r2, g[w], r)\r\n    }\r\n\r\n    while (j >= 0 && (e[j] & (1 << i)) == 0) {\r\n      z.sqrTo(r, r2); t = r; r = r2; r2 = t\r\n      if (--i < 0) { i = this.DB - 1; --j }\r\n    }\r\n  }\r\n  return z.revert(r)\r\n}\r\n\r\n// (public) gcd(this,a) (HAC 14.54)\r\nfunction bnGCD (a) {\r\n  var x = (this.s < 0) ? this.negate() : this.clone()\r\n  var y = (a.s < 0) ? a.negate() : a.clone()\r\n  if (x.compareTo(y) < 0) { var t = x; x = y; y = t }\r\n  var i = x.getLowestSetBit(); var g = y.getLowestSetBit()\r\n  if (g < 0) return x\r\n  if (i < g) g = i\r\n  if (g > 0) {\r\n    x.rShiftTo(g, x)\r\n    y.rShiftTo(g, y)\r\n  }\r\n  while (x.signum() > 0) {\r\n    if ((i = x.getLowestSetBit()) > 0) x.rShiftTo(i, x)\r\n    if ((i = y.getLowestSetBit()) > 0) y.rShiftTo(i, y)\r\n    if (x.compareTo(y) >= 0) {\r\n      x.subTo(y, x)\r\n      x.rShiftTo(1, x)\r\n    } else {\r\n      y.subTo(x, y)\r\n      y.rShiftTo(1, y)\r\n    }\r\n  }\r\n  if (g > 0) y.lShiftTo(g, y)\r\n  return y\r\n}\r\n\r\n// (protected) this % n, n < 2^26\r\nfunction bnpModInt (n) {\r\n  if (n <= 0) return 0\r\n  var d = this.DV % n; var r = (this.s < 0) ? n - 1 : 0\r\n  if (this.t > 0) {\r\n    if (d == 0) r = this[0] % n\r\n    else for (var i = this.t - 1; i >= 0; --i) r = (d * r + this[i]) % n\r\n  }\r\n  return r\r\n}\r\n\r\n// (public) 1/this % m (HAC 14.61)\r\nfunction bnModInverse (m) {\r\n  var ac = m.isEven()\r\n  if ((this.isEven() && ac) || m.signum() == 0) return BigInteger.ZERO\r\n  var u = m.clone(); var v = this.clone()\r\n  var a = nbv(1); var b = nbv(0); var c = nbv(0); var d = nbv(1)\r\n  while (u.signum() != 0) {\r\n    while (u.isEven()) {\r\n      u.rShiftTo(1, u)\r\n      if (ac) {\r\n        if (!a.isEven() || !b.isEven()) { a.addTo(this, a); b.subTo(m, b) }\r\n        a.rShiftTo(1, a)\r\n      } else if (!b.isEven()) b.subTo(m, b)\r\n      b.rShiftTo(1, b)\r\n    }\r\n    while (v.isEven()) {\r\n      v.rShiftTo(1, v)\r\n      if (ac) {\r\n        if (!c.isEven() || !d.isEven()) { c.addTo(this, c); d.subTo(m, d) }\r\n        c.rShiftTo(1, c)\r\n      } else if (!d.isEven()) d.subTo(m, d)\r\n      d.rShiftTo(1, d)\r\n    }\r\n    if (u.compareTo(v) >= 0) {\r\n      u.subTo(v, u)\r\n      if (ac) a.subTo(c, a)\r\n      b.subTo(d, b)\r\n    } else {\r\n      v.subTo(u, v)\r\n      if (ac) c.subTo(a, c)\r\n      d.subTo(b, d)\r\n    }\r\n  }\r\n  if (v.compareTo(BigInteger.ONE) != 0) return BigInteger.ZERO\r\n  if (d.compareTo(m) >= 0) return d.subtract(m)\r\n  if (d.signum() < 0) d.addTo(m, d); else return d\r\n  if (d.signum() < 0) return d.add(m); else return d\r\n}\r\n\r\nvar lowprimes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97, 101, 103, 107, 109, 113, 127, 131, 137, 139, 149, 151, 157, 163, 167, 173, 179, 181, 191, 193, 197, 199, 211, 223, 227, 229, 233, 239, 241, 251, 257, 263, 269, 271, 277, 281, 283, 293, 307, 311, 313, 317, 331, 337, 347, 349, 353, 359, 367, 373, 379, 383, 389, 397, 401, 409, 419, 421, 431, 433, 439, 443, 449, 457, 461, 463, 467, 479, 487, 491, 499, 503, 509, 521, 523, 541, 547, 557, 563, 569, 571, 577, 587, 593, 599, 601, 607, 613, 617, 619, 631, 641, 643, 647, 653, 659, 661, 673, 677, 683, 691, 701, 709, 719, 727, 733, 739, 743, 751, 757, 761, 769, 773, 787, 797, 809, 811, 821, 823, 827, 829, 839, 853, 857, 859, 863, 877, 881, 883, 887, 907, 911, 919, 929, 937, 941, 947, 953, 967, 971, 977, 983, 991, 997]\r\nvar lplim = (1 << 26) / lowprimes[lowprimes.length - 1]\r\n\r\n// (public) test primality with certainty >= 1-.5^t\r\nfunction bnIsProbablePrime (t) {\r\n  var i; var x = this.abs()\r\n  if (x.t == 1 && x[0] <= lowprimes[lowprimes.length - 1]) {\r\n    for (i = 0; i < lowprimes.length; ++i) { if (x[0] == lowprimes[i]) return true }\r\n    return false\r\n  }\r\n  if (x.isEven()) return false\r\n  i = 1\r\n  while (i < lowprimes.length) {\r\n    var m = lowprimes[i]; var j = i + 1\r\n    while (j < lowprimes.length && m < lplim) m *= lowprimes[j++]\r\n    m = x.modInt(m)\r\n    while (i < j) if (m % lowprimes[i++] == 0) return false\r\n  }\r\n  return x.millerRabin(t)\r\n}\r\n\r\n// (protected) true if probably prime (HAC 4.24, Miller-Rabin)\r\nfunction bnpMillerRabin (t) {\r\n  var n1 = this.subtract(BigInteger.ONE)\r\n  var k = n1.getLowestSetBit()\r\n  if (k <= 0) return false\r\n  var r = n1.shiftRight(k)\r\n  t = (t + 1) >> 1\r\n  if (t > lowprimes.length) t = lowprimes.length\r\n  var a = nbi()\r\n  for (var i = 0; i < t; ++i) {\r\n    // Pick bases at random, instead of starting at 2\r\n    a.fromInt(lowprimes[Math.floor(Math.random() * lowprimes.length)])\r\n    var y = a.modPow(r, this)\r\n    if (y.compareTo(BigInteger.ONE) != 0 && y.compareTo(n1) != 0) {\r\n      var j = 1\r\n      while (j++ < k && y.compareTo(n1) != 0) {\r\n        y = y.modPowInt(2, this)\r\n        if (y.compareTo(BigInteger.ONE) == 0) return false\r\n      }\r\n      if (y.compareTo(n1) != 0) return false\r\n    }\r\n  }\r\n  return true\r\n}\r\n\r\n// protected\r\nBigInteger.prototype.chunkSize = bnpChunkSize\r\nBigInteger.prototype.toRadix = bnpToRadix\r\nBigInteger.prototype.fromRadix = bnpFromRadix\r\nBigInteger.prototype.fromNumber = bnpFromNumber\r\nBigInteger.prototype.bitwiseTo = bnpBitwiseTo\r\nBigInteger.prototype.changeBit = bnpChangeBit\r\nBigInteger.prototype.addTo = bnpAddTo\r\nBigInteger.prototype.dMultiply = bnpDMultiply\r\nBigInteger.prototype.dAddOffset = bnpDAddOffset\r\nBigInteger.prototype.multiplyLowerTo = bnpMultiplyLowerTo\r\nBigInteger.prototype.multiplyUpperTo = bnpMultiplyUpperTo\r\nBigInteger.prototype.modInt = bnpModInt\r\nBigInteger.prototype.millerRabin = bnpMillerRabin\r\n\r\n// public\r\nBigInteger.prototype.clone = bnClone\r\nBigInteger.prototype.intValue = bnIntValue\r\nBigInteger.prototype.byteValue = bnByteValue\r\nBigInteger.prototype.shortValue = bnShortValue\r\nBigInteger.prototype.signum = bnSigNum\r\nBigInteger.prototype.toByteArray = bnToByteArray\r\nBigInteger.prototype.equals = bnEquals\r\nBigInteger.prototype.min = bnMin\r\nBigInteger.prototype.max = bnMax\r\nBigInteger.prototype.and = bnAnd\r\nBigInteger.prototype.or = bnOr\r\nBigInteger.prototype.xor = bnXor\r\nBigInteger.prototype.andNot = bnAndNot\r\nBigInteger.prototype.not = bnNot\r\nBigInteger.prototype.shiftLeft = bnShiftLeft\r\nBigInteger.prototype.shiftRight = bnShiftRight\r\nBigInteger.prototype.getLowestSetBit = bnGetLowestSetBit\r\nBigInteger.prototype.bitCount = bnBitCount\r\nBigInteger.prototype.testBit = bnTestBit\r\nBigInteger.prototype.setBit = bnSetBit\r\nBigInteger.prototype.clearBit = bnClearBit\r\nBigInteger.prototype.flipBit = bnFlipBit\r\nBigInteger.prototype.add = bnAdd\r\nBigInteger.prototype.subtract = bnSubtract\r\nBigInteger.prototype.multiply = bnMultiply\r\nBigInteger.prototype.divide = bnDivide\r\nBigInteger.prototype.remainder = bnRemainder\r\nBigInteger.prototype.divideAndRemainder = bnDivideAndRemainder\r\nBigInteger.prototype.modPow = bnModPow\r\nBigInteger.prototype.modInverse = bnModInverse\r\nBigInteger.prototype.pow = bnPow\r\nBigInteger.prototype.gcd = bnGCD\r\nBigInteger.prototype.isProbablePrime = bnIsProbablePrime\r\n\r\n// JSBN-specific extension\r\nBigInteger.prototype.square = bnSquare\r\n\r\n// Expose the Barrett function\r\nBigInteger.prototype.Barrett = Barrett\r\n\r\n// BigInteger interfaces not implemented in jsbn:\r\n\r\n// BigInteger(int signum, byte[] magnitude)\r\n// double doubleValue()\r\n// float floatValue()\r\n// int hashCode()\r\n// long longValue()\r\n// static BigInteger valueOf(long val)\r\n\r\n// Random number generator - requires a PRNG backend, e.g. prng4.js\r\n\r\n// For best results, put code like\r\n// <body onClick='rng_seed_time();' onKeyPress='rng_seed_time();'>\r\n// in your main HTML document.\r\n\r\nvar rng_state\r\nvar rng_pool\r\nvar rng_pptr\r\n\r\n// Mix in a 32-bit integer into the pool\r\nfunction rng_seed_int (x) {\r\n  rng_pool[rng_pptr++] ^= x & 255\r\n  rng_pool[rng_pptr++] ^= (x >> 8) & 255\r\n  rng_pool[rng_pptr++] ^= (x >> 16) & 255\r\n  rng_pool[rng_pptr++] ^= (x >> 24) & 255\r\n  if (rng_pptr >= rng_psize) rng_pptr -= rng_psize\r\n}\r\n\r\n// Mix in the current time (w/milliseconds) into the pool\r\nfunction rng_seed_time () {\r\n  rng_seed_int(new Date().getTime())\r\n}\r\n\r\nvar rng_psize = 256\r\n\r\n// Initialize the pool with junk if needed.\r\nif (rng_pool == null) {\r\n  rng_pool = []\r\n  rng_pptr = 0\r\n  var t\r\n  if (typeof window !== 'undefined' && window.crypto) {\r\n    if (window.crypto.getRandomValues) {\r\n      // Use webcrypto if available\r\n      var ua = new Uint8Array(32)\r\n      window.crypto.getRandomValues(ua)\r\n      for (t = 0; t < 32; ++t) { rng_pool[rng_pptr++] = ua[t] }\r\n    } else if (navigator.appName == 'Netscape' && navigator.appVersion < '5') {\r\n      // Extract entropy (256 bits) from NS4 RNG if available\r\n      var z = window.crypto.random(32)\r\n      for (t = 0; t < z.length; ++t) { rng_pool[rng_pptr++] = z.charCodeAt(t) & 255 }\r\n    }\r\n  }\r\n  while (rng_pptr < rng_psize) { // extract some randomness from Math.random()\r\n    t = Math.floor(65536 * Math.random())\r\n    rng_pool[rng_pptr++] = t >>> 8\r\n    rng_pool[rng_pptr++] = t & 255\r\n  }\r\n  rng_pptr = 0\r\n  rng_seed_time()\r\n  // rng_seed_int(window.screenX);\r\n  // rng_seed_int(window.screenY);\r\n}\r\n\r\nfunction rng_get_byte () {\r\n  if (rng_state == null) {\r\n    rng_seed_time()\r\n    rng_state = prng_newstate()\r\n    rng_state.init(rng_pool)\r\n    for (rng_pptr = 0; rng_pptr < rng_pool.length; ++rng_pptr) { rng_pool[rng_pptr] = 0 }\r\n    rng_pptr = 0\r\n    // rng_pool = null;\r\n  }\r\n  return rng_state.next()\r\n}\r\n\r\nfunction rng_get_bytes (ba) {\r\n  var i\r\n  for (i = 0; i < ba.length; ++i) ba[i] = rng_get_byte()\r\n}\r\n\r\nfunction SecureRandom () {}\r\n\r\nSecureRandom.prototype.nextBytes = rng_get_bytes\r\n\r\n// prng4.js - uses Arcfour as a PRNG\r\n\r\nfunction Arcfour () {\r\n  this.i = 0\r\n  this.j = 0\r\n  this.S = []\r\n}\r\n\r\n// Initialize arcfour context from key, an array of ints, each from [0..255]\r\nfunction ARC4init (key) {\r\n  var i, j, t\r\n  for (i = 0; i < 256; ++i) { this.S[i] = i }\r\n  j = 0\r\n  for (i = 0; i < 256; ++i) {\r\n    j = (j + this.S[i] + key[i % key.length]) & 255\r\n    t = this.S[i]\r\n    this.S[i] = this.S[j]\r\n    this.S[j] = t\r\n  }\r\n  this.i = 0\r\n  this.j = 0\r\n}\r\n\r\nfunction ARC4next () {\r\n  var t\r\n  this.i = (this.i + 1) & 255\r\n  this.j = (this.j + this.S[this.i]) & 255\r\n  t = this.S[this.i]\r\n  this.S[this.i] = this.S[this.j]\r\n  this.S[this.j] = t\r\n  return this.S[(t + this.S[this.i]) & 255]\r\n}\r\n\r\nArcfour.prototype.init = ARC4init\r\nArcfour.prototype.next = ARC4next\r\n\r\n// Plug in your RNG constructor here\r\nfunction prng_newstate () {\r\n  return new Arcfour()\r\n}\r\n\r\n// Pool size must be a multiple of 4 and greater than 32.\r\n// An array of bytes the size of the pool will be passed to init()\r\n\r\n\r\n\n\n//# sourceURL=webpack://SM/./src/sm2/jsbn.js?");

        /***/
}),

/***/ "./src/sm2/sm2.js":
/*!************************!*\
  !*** ./src/sm2/sm2.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

        eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _jsbn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./jsbn.js */ \"./src/sm2/jsbn.js\");\n/* harmony import */ var _sm3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sm3 */ \"./src/sm2/sm3.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"./src/sm2/utils.js\");\n/* eslint-disable eqeqeq */\r\n\r\n\r\n\r\n\r\nclass SM2Cipher {\r\n  constructor () {\r\n    this.ct = 1\r\n    this.p2 = null\r\n    this.sm3keybase = null\r\n    this.sm3c3 = null\r\n    this.key = new Array(32)\r\n    this.keyOff = 0\r\n  }\r\n\r\n  reset () {\r\n    this.sm3keybase = new _sm3__WEBPACK_IMPORTED_MODULE_1__[\"default\"]()\r\n    this.sm3c3 = new _sm3__WEBPACK_IMPORTED_MODULE_1__[\"default\"]()\r\n    const xWords = _utils__WEBPACK_IMPORTED_MODULE_2__[\"default\"].hexToArray(this.p2.getX().toBigInteger().toRadix(16))\r\n    const yWords = _utils__WEBPACK_IMPORTED_MODULE_2__[\"default\"].hexToArray(this.p2.getY().toBigInteger().toRadix(16))\r\n    this.sm3keybase.blockUpdate(xWords, 0, xWords.length)\r\n    this.sm3c3.blockUpdate(xWords, 0, xWords.length)\r\n    this.sm3keybase.blockUpdate(yWords, 0, yWords.length)\r\n    this.ct = 1\r\n    this.nextKey()\r\n  }\r\n\r\n  nextKey () {\r\n    const sm3keycur = new _sm3__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this.sm3keybase)\r\n    sm3keycur.update((this.ct >> 24 & 0x00ff))\r\n    sm3keycur.update((this.ct >> 16 & 0x00ff))\r\n    sm3keycur.update((this.ct >> 8 & 0x00ff))\r\n    sm3keycur.update((this.ct & 0x00ff))\r\n    sm3keycur.doFinal(this.key, 0)\r\n    this.keyOff = 0\r\n    this.ct++\r\n  }\r\n\r\n  initEncipher (userKey) {\r\n    const keypair = _utils__WEBPACK_IMPORTED_MODULE_2__[\"default\"].generateKeyPairHex()\r\n    const k = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger(keypair.privateKey, 16)\r\n    let publicKey = keypair.publicKey\r\n\r\n    this.p2 = userKey.multiply(k) // [k](Pb)\r\n    this.reset()\r\n\r\n    if (publicKey.length > 128) {\r\n      publicKey = publicKey.substr(publicKey.length - 128)\r\n    }\r\n\r\n    return publicKey\r\n  }\r\n\r\n  encryptBlock (data) {\r\n    this.sm3c3.blockUpdate(data, 0, data.length)\r\n    for (let i = 0; i < data.length; i++) {\r\n      if (this.keyOff == this.key.length) {\r\n        this.nextKey()\r\n      }\r\n      data[i] ^= this.key[this.keyOff++] & 0xff\r\n    }\r\n  }\r\n\r\n  initDecipher (userD, c1) {\r\n    this.p2 = c1.multiply(userD)\r\n    this.reset()\r\n  }\r\n\r\n  decryptBlock (data) {\r\n    for (let i = 0; i < data.length; i++) {\r\n      if (this.keyOff == this.key.length) {\r\n        this.nextKey()\r\n      }\r\n      data[i] ^= this.key[this.keyOff++] & 0xff\r\n    }\r\n    this.sm3c3.blockUpdate(data, 0, data.length)\r\n  }\r\n\r\n  doFinal (c3) {\r\n    const yWords = _utils__WEBPACK_IMPORTED_MODULE_2__[\"default\"].hexToArray(this.p2.getY().toBigInteger().toRadix(16))\r\n    this.sm3c3.blockUpdate(yWords, 0, yWords.length)\r\n    this.sm3c3.doFinal(c3, 0)\r\n    this.reset()\r\n  }\r\n\r\n  createPoint (x, y) {\r\n    const publicKey = '04' + x + y\r\n    const point = _utils__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getGlobalCurve().decodePointHex(publicKey)\r\n    return point\r\n  }\r\n}\r\n\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SM2Cipher);\r\n\n\n//# sourceURL=webpack://SM/./src/sm2/sm2.js?");

        /***/
}),

/***/ "./src/sm2/sm3.js":
/*!************************!*\
  !*** ./src/sm2/sm3.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

        eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _jsbn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./jsbn.js */ \"./src/sm2/jsbn.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"./src/sm2/utils.js\");\n/* eslint-disable eqeqeq */\r\n/* eslint-disable camelcase */\r\n\r\n\r\n\r\nconst copyArray = function (sourceArray, sourceIndex, destinationArray, destinationIndex, length) {\r\n  for (let i = 0; i < length; i++) destinationArray[destinationIndex + i] = sourceArray[sourceIndex + i]\r\n}\r\n\r\nconst Int32 = {\r\n  minValue: -parseInt('10000000000000000000000000000000', 2),\r\n  maxValue: parseInt('1111111111111111111111111111111', 2),\r\n  parse: function (n) {\r\n    if (n < this.minValue) {\r\n      const bigInteger = -n\r\n      const bigIntegerRadix = bigInteger.toString(2)\r\n      const subBigIntegerRadix = bigIntegerRadix.substr(bigIntegerRadix.length - 31, 31)\r\n      let reBigIntegerRadix = ''\r\n      for (let i = 0; i < subBigIntegerRadix.length; i++) {\r\n        const subBigIntegerRadixItem = subBigIntegerRadix.substr(i, 1)\r\n        reBigIntegerRadix += subBigIntegerRadixItem == '0' ? '1' : '0'\r\n      }\r\n      const result = parseInt(reBigIntegerRadix, 2)\r\n      return (result + 1)\r\n    } else if (n > this.maxValue) {\r\n      const bigInteger = Number(n)\r\n      const bigIntegerRadix = bigInteger.toString(2)\r\n      const subBigIntegerRadix = bigIntegerRadix.substr(bigIntegerRadix.length - 31, 31)\r\n      let reBigIntegerRadix = ''\r\n      for (let i = 0; i < subBigIntegerRadix.length; i++) {\r\n        const subBigIntegerRadixItem = subBigIntegerRadix.substr(i, 1)\r\n        reBigIntegerRadix += subBigIntegerRadixItem == '0' ? '1' : '0'\r\n      }\r\n      const result = parseInt(reBigIntegerRadix, 2)\r\n      return -(result + 1)\r\n    } else {\r\n      return n\r\n    }\r\n  },\r\n  parseByte: function (n) {\r\n    if (n < 0) {\r\n      const bigInteger = -n\r\n      const bigIntegerRadix = bigInteger.toString(2)\r\n      const subBigIntegerRadix = bigIntegerRadix.substr(bigIntegerRadix.length - 8, 8)\r\n      let reBigIntegerRadix = ''\r\n      for (let i = 0; i < subBigIntegerRadix.length; i++) {\r\n        const subBigIntegerRadixItem = subBigIntegerRadix.substr(i, 1)\r\n        reBigIntegerRadix += subBigIntegerRadixItem == '0' ? '1' : '0'\r\n      }\r\n      const result = parseInt(reBigIntegerRadix, 2)\r\n      return (result + 1)\r\n    } else if (n > 255) {\r\n      const bigInteger = Number(n)\r\n      const bigIntegerRadix = bigInteger.toString(2)\r\n      return parseInt(bigIntegerRadix.substr(bigIntegerRadix.length - 8, 8), 2)\r\n    } else {\r\n      return n\r\n    }\r\n  }\r\n}\r\n\r\nclass SM3Digest {\r\n  constructor () {\r\n    this.xBuf = []\r\n    this.xBufOff = 0\r\n    this.byteCount = 0\r\n    this.DIGEST_LENGTH = 32\r\n    this.v0 = [0x7380166f, 0x4914b2b9, 0x172442d7, 0xda8a0600, 0xa96f30bc, 0x163138aa, 0xe38dee4d, 0xb0fb0e4e]\r\n    this.v0 = [0x7380166f, 0x4914b2b9, 0x172442d7, -628488704, -1452330820, 0x163138aa, -477237683, -1325724082]\r\n    this.v = new Array(8)\r\n    this.v_ = new Array(8)\r\n    this.X0 = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\r\n    this.X = new Array(68)\r\n    this.xOff = 0\r\n    this.T_00_15 = 0x79cc4519\r\n    this.T_16_63 = 0x7a879d8a\r\n    if (arguments.length > 0) {\r\n      this.initDigest(arguments[0])\r\n    } else {\r\n      this.init()\r\n    }\r\n  }\r\n\r\n  init () {\r\n    this.xBuf = new Array(4)\r\n    this.reset()\r\n  }\r\n\r\n  initDigest (t) {\r\n    this.xBuf = [].concat(t.xBuf)\r\n    this.xBufOff = t.xBufOff\r\n    this.byteCount = t.byteCount\r\n    copyArray(t.X, 0, this.X, 0, t.X.length)\r\n    this.xOff = t.xOff\r\n    copyArray(t.v, 0, this.v, 0, t.v.length)\r\n  }\r\n\r\n  getDigestSize () {\r\n    return this.DIGEST_LENGTH\r\n  }\r\n\r\n  reset () {\r\n    this.byteCount = 0\r\n    this.xBufOff = 0\r\n    for (const elem in this.xBuf) this.xBuf[elem] = null\r\n    copyArray(this.v0, 0, this.v, 0, this.v0.length)\r\n    this.xOff = 0\r\n    copyArray(this.X0, 0, this.X, 0, this.X0.length)\r\n  }\r\n\r\n  processBlock () {\r\n    let i\r\n    const ww = this.X\r\n    const ww_ = new Array(64)\r\n    for (i = 16; i < 68; i++) {\r\n      ww[i] = this.p1(ww[i - 16] ^ ww[i - 9] ^ (this.rotate(ww[i - 3], 15))) ^ (this.rotate(ww[i - 13], 7)) ^ ww[i - 6]\r\n    }\r\n    for (i = 0; i < 64; i++) {\r\n      ww_[i] = ww[i] ^ ww[i + 4]\r\n    }\r\n    const vv = this.v\r\n    const vv_ = this.v_\r\n    copyArray(vv, 0, vv_, 0, this.v0.length)\r\n    let SS1, SS2, TT1, TT2, aaa\r\n    for (i = 0; i < 16; i++) {\r\n      aaa = this.rotate(vv_[0], 12)\r\n      SS1 = Int32.parse(Int32.parse(aaa + vv_[4]) + this.rotate(this.T_00_15, i))\r\n      SS1 = this.rotate(SS1, 7)\r\n      SS2 = SS1 ^ aaa\r\n      TT1 = Int32.parse(Int32.parse(this.ff_00_15(vv_[0], vv_[1], vv_[2]) + vv_[3]) + SS2) + ww_[i]\r\n      TT2 = Int32.parse(Int32.parse(this.gg_00_15(vv_[4], vv_[5], vv_[6]) + vv_[7]) + SS1) + ww[i]\r\n      vv_[3] = vv_[2]\r\n      vv_[2] = this.rotate(vv_[1], 9)\r\n      vv_[1] = vv_[0]\r\n      vv_[0] = TT1\r\n      vv_[7] = vv_[6]\r\n      vv_[6] = this.rotate(vv_[5], 19)\r\n      vv_[5] = vv_[4]\r\n      vv_[4] = this.p0(TT2)\r\n    }\r\n    for (i = 16; i < 64; i++) {\r\n      aaa = this.rotate(vv_[0], 12)\r\n      SS1 = Int32.parse(Int32.parse(aaa + vv_[4]) + this.rotate(this.T_16_63, i))\r\n      SS1 = this.rotate(SS1, 7)\r\n      SS2 = SS1 ^ aaa\r\n      TT1 = Int32.parse(Int32.parse(this.ff_16_63(vv_[0], vv_[1], vv_[2]) + vv_[3]) + SS2) + ww_[i]\r\n      TT2 = Int32.parse(Int32.parse(this.gg_16_63(vv_[4], vv_[5], vv_[6]) + vv_[7]) + SS1) + ww[i]\r\n      vv_[3] = vv_[2]\r\n      vv_[2] = this.rotate(vv_[1], 9)\r\n      vv_[1] = vv_[0]\r\n      vv_[0] = TT1\r\n      vv_[7] = vv_[6]\r\n      vv_[6] = this.rotate(vv_[5], 19)\r\n      vv_[5] = vv_[4]\r\n      vv_[4] = this.p0(TT2)\r\n    }\r\n    for (i = 0; i < 8; i++) {\r\n      vv[i] ^= Int32.parse(vv_[i])\r\n    }\r\n    this.xOff = 0\r\n    copyArray(this.X0, 0, this.X, 0, this.X0.length)\r\n  }\r\n\r\n  processWord (in_Renamed, inOff) {\r\n    let n = in_Renamed[inOff] << 24\r\n    n |= (in_Renamed[++inOff] & 0xff) << 16\r\n    n |= (in_Renamed[++inOff] & 0xff) << 8\r\n    n |= (in_Renamed[++inOff] & 0xff)\r\n    this.X[this.xOff] = n\r\n    if (++this.xOff == 16) {\r\n      this.processBlock()\r\n    }\r\n  }\r\n\r\n  processLength (bitLength) {\r\n    if (this.xOff > 14) {\r\n      this.processBlock()\r\n    }\r\n    this.X[14] = (this.urShiftLong(bitLength, 32))\r\n    this.X[15] = (bitLength & (0xffffffff))\r\n  }\r\n\r\n  intToBigEndian (n, bs, off) {\r\n    bs[off] = Int32.parseByte(this.urShift(n, 24))\r\n    bs[++off] = Int32.parseByte(this.urShift(n, 16))\r\n    bs[++off] = Int32.parseByte(this.urShift(n, 8))\r\n    bs[++off] = Int32.parseByte(n)\r\n  }\r\n\r\n  doFinal (out_Renamed, outOff) {\r\n    this.finish()\r\n    for (let i = 0; i < 8; i++) {\r\n      this.intToBigEndian(this.v[i], out_Renamed, outOff + i * 4)\r\n    }\r\n    this.reset()\r\n    return this.DIGEST_LENGTH\r\n  }\r\n\r\n  update (input) {\r\n    this.xBuf[this.xBufOff++] = input\r\n    if (this.xBufOff == this.xBuf.length) {\r\n      this.processWord(this.xBuf, 0)\r\n      this.xBufOff = 0\r\n    }\r\n    this.byteCount++\r\n  }\r\n\r\n  blockUpdate (input, inOff, length) {\r\n    while ((this.xBufOff !== 0) && (length > 0)) {\r\n      this.update(input[inOff])\r\n      inOff++\r\n      length--\r\n    }\r\n    while (length > this.xBuf.length) {\r\n      this.processWord(input, inOff)\r\n      inOff += this.xBuf.length\r\n      length -= this.xBuf.length\r\n      this.byteCount += this.xBuf.length\r\n    }\r\n    while (length > 0) {\r\n      this.update(input[inOff])\r\n      inOff++\r\n      length--\r\n    }\r\n  }\r\n\r\n  finish () {\r\n    const bitLength = (this.byteCount << 3)\r\n    this.update((128))\r\n    while (this.xBufOff !== 0) this.update((0))\r\n    this.processLength(bitLength)\r\n    this.processBlock()\r\n  }\r\n\r\n  rotate (x, n) {\r\n    return (x << n) | (this.urShift(x, (32 - n)))\r\n  }\r\n\r\n  p0 (X) {\r\n    return ((X) ^ this.rotate((X), 9) ^ this.rotate((X), 17))\r\n  }\r\n\r\n  p1 (X) {\r\n    return ((X) ^ this.rotate((X), 15) ^ this.rotate((X), 23))\r\n  }\r\n\r\n  ff_00_15 (X, Y, Z) {\r\n    return (X ^ Y ^ Z)\r\n  }\r\n\r\n  ff_16_63 (X, Y, Z) {\r\n    return ((X & Y) | (X & Z) | (Y & Z))\r\n  }\r\n\r\n  gg_00_15 (X, Y, Z) {\r\n    return (X ^ Y ^ Z)\r\n  }\r\n\r\n  gg_16_63 (X, Y, Z) {\r\n    return ((X & Y) | (~X & Z))\r\n  }\r\n\r\n  urShift (number, bits) {\r\n    if (number > Int32.maxValue || number < Int32.minValue) {\r\n      number = Int32.parse(number)\r\n    }\r\n    if (number >= 0) {\r\n      return number >> bits\r\n    } else {\r\n      return (number >> bits) + (2 << ~bits)\r\n    }\r\n  }\r\n\r\n  urShiftLong (number, bits) {\r\n    let returnV\r\n    const big = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger()\r\n    big.fromInt(number)\r\n    if (big.signum() >= 0) {\r\n      returnV = big.shiftRight(bits).intValue()\r\n    } else {\r\n      const bigAdd = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger()\r\n      bigAdd.fromInt(2)\r\n      const shiftLeftBits = ~bits\r\n      let shiftLeftNumber = ''\r\n      if (shiftLeftBits < 0) {\r\n        const shiftRightBits = 64 + shiftLeftBits\r\n        for (let i = 0; i < shiftRightBits; i++) {\r\n          shiftLeftNumber += '0'\r\n        }\r\n        const shiftLeftNumberBigAdd = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger()\r\n        shiftLeftNumberBigAdd.fromInt(number >> bits)\r\n        const shiftLeftNumberBig = new _jsbn_js__WEBPACK_IMPORTED_MODULE_0__.BigInteger('10' + shiftLeftNumber, 2)\r\n        shiftLeftNumber = shiftLeftNumberBig.toRadix(10)\r\n        const r = shiftLeftNumberBig.add(shiftLeftNumberBigAdd)\r\n        returnV = r.toRadix(10)\r\n      } else {\r\n        shiftLeftNumber = bigAdd.shiftLeft((~bits)).intValue()\r\n        returnV = (number >> bits) + shiftLeftNumber\r\n      }\r\n    }\r\n    return returnV\r\n  }\r\n\r\n  getZ (g, publicKey) {\r\n    const userId = _utils__WEBPACK_IMPORTED_MODULE_1__[\"default\"].parseUtf8StringToHex('1234567812345678')\r\n    const len = userId.length * 4\r\n    this.update((len >> 8 & 0x00ff))\r\n    this.update((len & 0x00ff))\r\n    const userIdWords = _utils__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hexToArray(userId)\r\n    this.blockUpdate(userIdWords, 0, userIdWords.length)\r\n    const aWords = _utils__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hexToArray(g.curve.a.toBigInteger().toRadix(16))\r\n    const bWords = _utils__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hexToArray(g.curve.b.toBigInteger().toRadix(16))\r\n    const gxWords = _utils__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hexToArray(g.getX().toBigInteger().toRadix(16))\r\n    const gyWords = _utils__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hexToArray(g.getY().toBigInteger().toRadix(16))\r\n    const pxWords = _utils__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hexToArray(publicKey.substr(0, 64))\r\n    const pyWords = _utils__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hexToArray(publicKey.substr(64, 64))\r\n    this.blockUpdate(aWords, 0, aWords.length)\r\n    this.blockUpdate(bWords, 0, bWords.length)\r\n    this.blockUpdate(gxWords, 0, gxWords.length)\r\n    this.blockUpdate(gyWords, 0, gyWords.length)\r\n    this.blockUpdate(pxWords, 0, pxWords.length)\r\n    this.blockUpdate(pyWords, 0, pyWords.length)\r\n    const md = new Array(this.getDigestSize())\r\n    this.doFinal(md, 0)\r\n    return md\r\n  }\r\n}\r\n\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SM3Digest);\r\n\n\n//# sourceURL=webpack://SM/./src/sm2/sm3.js?");

        /***/
}),

/***/ "./src/sm2/utils.js":
/*!**************************!*\
  !*** ./src/sm2/utils.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

        eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ec__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ec */ \"./src/sm2/ec.js\");\n/* harmony import */ var _jsbn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./jsbn.js */ \"./src/sm2/jsbn.js\");\n/* eslint-disable eqeqeq */\r\n\r\n\r\n\r\nconst rng = new _jsbn_js__WEBPACK_IMPORTED_MODULE_1__.SecureRandom()\r\nconst { curve, G, n } = generateEcparam()\r\n\r\n/**\r\n * 获取公共椭圆曲线\r\n */\r\nfunction getGlobalCurve () {\r\n  return curve\r\n}\r\n\r\n/**\r\n * 生成ecparam\r\n */\r\nfunction generateEcparam () {\r\n  // 椭圆曲线\r\n  const p = new _jsbn_js__WEBPACK_IMPORTED_MODULE_1__.BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF', 16)\r\n  const a = new _jsbn_js__WEBPACK_IMPORTED_MODULE_1__.BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC', 16)\r\n  const b = new _jsbn_js__WEBPACK_IMPORTED_MODULE_1__.BigInteger('28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93', 16)\r\n  const curve = new _ec__WEBPACK_IMPORTED_MODULE_0__.ECCurveFp(p, a, b)\r\n\r\n  // 基点\r\n  const gxHex = '32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7'\r\n  const gyHex = 'BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0'\r\n  const G = curve.decodePointHex('04' + gxHex + gyHex)\r\n\r\n  const n = new _jsbn_js__WEBPACK_IMPORTED_MODULE_1__.BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123', 16)\r\n\r\n  return { curve, G, n }\r\n}\r\n\r\n/**\r\n * 生成密钥对\r\n */\r\nfunction generateKeyPairHex () {\r\n  const d = new _jsbn_js__WEBPACK_IMPORTED_MODULE_1__.BigInteger(n.bitLength(), rng).mod(n.subtract(_jsbn_js__WEBPACK_IMPORTED_MODULE_1__.BigInteger.ONE)).add(_jsbn_js__WEBPACK_IMPORTED_MODULE_1__.BigInteger.ONE) // 随机数\r\n  const privateKey = leftPad(d.toString(16), 64)\r\n\r\n  const P = G.multiply(d) // P = dG，p 为公钥，d 为私钥\r\n  const Px = leftPad(P.getX().toBigInteger().toString(16), 64)\r\n  const Py = leftPad(P.getY().toBigInteger().toString(16), 64)\r\n  const publicKey = '04' + Px + Py\r\n\r\n  return { privateKey, publicKey }\r\n}\r\n\r\n/**\r\n * 解析utf8字符串到16进制\r\n */\r\nfunction parseUtf8StringToHex (input) {\r\n  input = unescape(encodeURIComponent(input))\r\n\r\n  const length = input.length\r\n\r\n  // 转换到字数组\r\n  const words = []\r\n  for (let i = 0; i < length; i++) {\r\n    words[i >>> 2] |= (input.charCodeAt(i) & 0xff) << (24 - (i % 4) * 8)\r\n  }\r\n\r\n  // 转换到16进制\r\n  const hexChars = []\r\n  for (let i = 0; i < length; i++) {\r\n    const bite = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff\r\n    hexChars.push((bite >>> 4).toString(16))\r\n    hexChars.push((bite & 0x0f).toString(16))\r\n  }\r\n\r\n  return hexChars.join('')\r\n}\r\n\r\n/**\r\n * 解析arrayBuffer到16进制字符串\r\n */\r\nfunction parseArrayBufferToHex (input) {\r\n  return Array.prototype.map.call(new Uint8Array(input), x => ('00' + x.toString(16)).slice(-2)).join('')\r\n}\r\n\r\n/**\r\n * 补全16进制字符串\r\n */\r\nfunction leftPad (input, num) {\r\n  if (input.length >= num) return input\r\n\r\n  return (new Array(num - input.length + 1)).join('0') + input\r\n}\r\n\r\n/**\r\n * 转成16进制串\r\n */\r\nfunction arrayToHex (arr) {\r\n  const words = []\r\n  let j = 0\r\n  for (let i = 0; i < arr.length * 2; i += 2) {\r\n    words[i >>> 3] |= parseInt(arr[j], 10) << (24 - (i % 8) * 4)\r\n    j++\r\n  }\r\n\r\n  // 转换到16进制\r\n  const hexChars = []\r\n  for (let i = 0; i < arr.length; i++) {\r\n    const bite = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff\r\n    hexChars.push((bite >>> 4).toString(16))\r\n    hexChars.push((bite & 0x0f).toString(16))\r\n  }\r\n\r\n  return hexChars.join('')\r\n}\r\n\r\n/**\r\n * 转成utf8串\r\n */\r\nfunction arrayToUtf8 (arr) {\r\n  const words = []\r\n  let j = 0\r\n  for (let i = 0; i < arr.length * 2; i += 2) {\r\n    words[i >>> 3] |= parseInt(arr[j], 10) << (24 - (i % 8) * 4)\r\n    j++\r\n  }\r\n\r\n  try {\r\n    const latin1Chars = []\r\n\r\n    for (let i = 0; i < arr.length; i++) {\r\n      const bite = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff\r\n      latin1Chars.push(String.fromCharCode(bite))\r\n    }\r\n\r\n    return decodeURIComponent(escape(latin1Chars.join('')))\r\n  } catch (e) {\r\n    throw new Error('Malformed UTF-8 data')\r\n  }\r\n}\r\n\r\n/**\r\n * 转成ascii码数组\r\n */\r\nfunction hexToArray (hexStr) {\r\n  const words = []\r\n  let hexStrLength = hexStr.length\r\n\r\n  if (hexStrLength % 2 != 0) {\r\n    hexStr = leftPad(hexStr, hexStrLength + 1)\r\n  }\r\n\r\n  hexStrLength = hexStr.length\r\n\r\n  for (let i = 0; i < hexStrLength; i += 2) {\r\n    words.push(parseInt(hexStr.substr(i, 2), 16))\r\n  }\r\n  return words\r\n}\r\n\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\r\n  getGlobalCurve,\r\n  generateEcparam,\r\n  generateKeyPairHex,\r\n  parseUtf8StringToHex,\r\n  parseArrayBufferToHex,\r\n  leftPad,\r\n  arrayToHex,\r\n  arrayToUtf8,\r\n  hexToArray\r\n});\r\n\n\n//# sourceURL=webpack://SM/./src/sm2/utils.js?");

        /***/
})

    /******/
});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__ (moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
      /******/
}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
      /******/
};
/******/
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
    /******/
}
/******/
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for (var key in definition) {
/******/ 				if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
          /******/
}
        /******/
}
      /******/
};
    /******/
})();
/******/
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
    /******/
})();
/******/
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
        /******/
}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
      /******/
};
    /******/
})();
/******/
/************************************************************************/
/******/
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./index.js");
/******/ 	SM = __webpack_exports__;
  /******/
  /******/
})()
  ;