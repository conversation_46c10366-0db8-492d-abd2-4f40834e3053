* {
  margin: 0;
  padding: 0;
}
/* 底部导航栏样式 */
.bottom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: #fff;
  border-top: 1px solid #e5e5e5;
  display: flex;
  z-index: 1000;
}
.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}
.tab-item.active {
  color: #1989fa;
}
.tab-item:not(.active) {
  color: #999;
}
.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.tab-text {
  font-size: 12px;
  line-height: 1;
}
/* 首页图标 */
.home-icon {
  background-image: url('../img/tab/home.png');
}
.home-icon.active {
  background-image: url('../img/tab/home_o.png');
}
/* 我的图标 */
.my-icon {
  background-image: url('../img/tab/my.png');
}
.my-icon.active {
  background-image: url('../img/tab/home_o.png');
}
.flex_box {
  display: flex;
}
.flex_wrap {
  flex-wrap: wrap;
}
.flex_align_center {
  align-items: center;
}
.flex_align_start {
  align-items: flex-start;
}
.flex_justify_between {
  justify-content: space-between;
}
.flex_justify_around {
  justify-content: space-around;
}
.flex_justify_center {
  justify-content: center;
}
.flex_direction_column {
  flex-direction: column;
}
.flex_1 {
  flex: 1;
}
.section-header {
  margin-bottom: 20px;
}
.section-line {
  width: 4px;
  height: 16px;
  background: linear-gradient(90deg, #104b8b 0%, #1a69bf 100%);
  border-radius: 2px;
  margin-right: 8px;
}
.section-title {
  font-size: 18px;
  color: #333;
  font-weight: 600;
  flex: 1;
}
.one_text {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}
.two_text {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.loading {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  text-align: center;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
