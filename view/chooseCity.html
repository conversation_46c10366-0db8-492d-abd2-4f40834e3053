<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>选择地区</title>
    <link rel="stylesheet" href="../css/vant.css" />
    <link rel="stylesheet" href="../css/common.css" />
    <style type="text/css">
      [v-cloak] {
        display: none;
      }

      .selectCiti {
        height: 100%;
      }

      .van-ellipsis,
      .van-tree-select__item {
        border-bottom: 0.5px solid #ebebeb;
      }

      .citiName {
        display: flex;
        height: 50px;
        align-items: center;
        background: #fff;
      }

      .citia {
        flex: 1;
        text-align: center;
        color: #000;
      }

      .citia + .citia {
        flex: 2;
      }

      .van-tree-select {
        height: calc(100vh - 150px) !important;
      }

      .van-tree-select__nav-item {
        text-align: center;
        font-weight: 400;
      }

      .van-sidebar-item--select,
      .van-sidebar-item--select:active {
        background: #fff;
        color: #006ef0;
        font-weight: bold !important;
      }

      .van-sidebar-item--select:before {
        background: #006ef0;
      }

      .van-tree-select__item--active {
        color: #006ef0;
        font-weight: bold !important;
      }

      .van-tree-select__nav-item {
        color: #000;
        font-size: 15px;
      }

      .van-tree-select__item {
        color: #000;
        font-weight: 400 !important;
        font-size: 15px;
      }

      .van-nav-bar__title {
        color: #333333;
      }

      .subBtn {
        width: 100%;
        height: 108px;
        display: flex;
        padding-top: 16px;
        justify-content: center;
      }

      .subBtn .van-button--round {
        width: 300px;
        margin: 0 auto;
        height: 40px;
        border-radius: 100px;
      }
    </style>
  </head>

  <body>
    <div class="body_box" id="app">
      <div class="selectCiti">
        <div class="citiName">
          <div class="citia">区市</div>
          <div class="citia">街道</div>
        </div>
        <van-tree-select :active-id.sync="activeId" :main-active-index.sync="activeIndex" @click-item="clickItem" @click-nav="clickNav" :items="areaTree" />
      </div>
      <div class="subBtn">
        <van-button round @click="onClickSure(false)" type="info">确定</van-button>
      </div>
    </div>
    <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
    <script type="text/javascript" src="../js/aes.js"></script>
    <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
    <script src="../js/vue.min.js"></script>
    <script src="../js/vant.min.js"></script>
    <script>
      // 获取地区私钥
      var areaKey = '00871c7d56551fb20bde67a129649e7faf95c05d9203d47ed6de529df5722303b4'
      // 获取地区公钥
      var publicKey = '045b4e75032df41d536233631f426ef247ac738049ab291d1612df02801cb1f37cf9ef7949e5f00cecdb55411b3822deef5b03b8ab48fa8b45ebc546d1d88c32f5'

      var app = new Vue({
        el: '#app',
        data: {
          activeId: '',
          areaTree: [
            {
              id: '371500',
              name: '聊城市',
              children: [
                {
                  id: '371502',
                  name: '东昌府区',
                  children: [
                    { id: '371502001', name: '古楼街道' },
                    { id: '371502002', name: '新区街道' },
                    { id: '371502003', name: '柳园街道' },
                    { id: '371502004', name: '闫寺街道' },
                    { id: '371502005', name: '道口铺街道' },
                    { id: '371502006', name: '香江街道' },
                    { id: '371502007', name: '凤凰街道' },
                    { id: '371502008', name: '湖西街道' },
                    { id: '371502009', name: '李海务街道' },
                    { id: '371502010', name: '侯营镇' },
                    { id: '371502011', name: '沙镇镇' },
                    { id: '371502012', name: '堂邑镇' },
                    { id: '371502013', name: '梁水镇' },
                    { id: '371502014', name: '斗虎屯镇' },
                    { id: '371502015', name: '郑家镇' },
                    { id: '371502016', name: '张炉集镇' },
                    { id: '371502017', name: '于集镇' },
                    { id: '371502018', name: '许营镇' },
                    { id: '371502019', name: '朱老庄镇' },
                    { id: '371502020', name: '顾官屯镇' },
                    { id: '371502021', name: '韩集镇' },
                    { id: '371502022', name: '广平镇' },
                    { id: '371502023', name: '北城街道' }
                  ]
                },
                {
                  id: '371521',
                  name: '阳谷县',
                  children: [
                    { id: '371521001', name: '博济桥街道' },
                    { id: '371521002', name: '侨润街道' },
                    { id: '371521003', name: '狮子楼街道' },
                    { id: '371521101', name: '阎楼镇' },
                    { id: '371521103', name: '阿城镇' },
                    { id: '371521104', name: '七级镇' },
                    { id: '371521105', name: '安乐镇' },
                    { id: '371521106', name: '定水镇' },
                    { id: '371521107', name: '石佛镇' },
                    { id: '371521108', name: '李台镇' },
                    { id: '371521109', name: '寿张镇' },
                    { id: '371521111', name: '十五里园镇' },
                    { id: '371521112', name: '张秋镇' },
                    { id: '371521113', name: '郭店屯镇' },
                    { id: '371521114', name: '西湖镇' },
                    { id: '371521115', name: '高庙王镇' },
                    { id: '371521116', name: '金斗营镇' },
                    { id: '371521203', name: '大布乡' }
                  ]
                },
                {
                  id: '371522',
                  name: '莘县',
                  children: [
                    { id: '371522001', name: '燕塔街道' },
                    { id: '371522002', name: '莘亭街道' },
                    { id: '371522003', name: '莘州街道' },
                    { id: '371522004', name: '东鲁街道' },
                    { id: '371522101', name: '张鲁镇' },
                    { id: '371522102', name: '朝城镇' },
                    { id: '371522103', name: '观城镇' },
                    { id: '371522104', name: '古城镇' },
                    { id: '371522105', name: '大张家镇' },
                    { id: '371522106', name: '古云镇' },
                    { id: '371522107', name: '十八里铺镇' },
                    { id: '371522109', name: '燕店镇' },
                    { id: '371522110', name: '董杜庄镇' },
                    { id: '371522111', name: '王奉镇' },
                    { id: '371522112', name: '樱桃园镇' },
                    { id: '371522113', name: '河店镇' },
                    { id: '371522114', name: '妹冢镇' },
                    { id: '371522115', name: '魏庄镇' },
                    { id: '371522116', name: '张寨镇' },
                    { id: '371522117', name: '大王寨镇' },
                    { id: '371522118', name: '徐庄镇' },
                    { id: '371522119', name: '王庄集镇' },
                    { id: '371522120', name: '柿子园镇' },
                    { id: '371522121', name: '俎店镇' }
                  ]
                },
                {
                  id: '371523',
                  name: '茌平区',
                  children: [
                    { id: '371523001', name: '振兴街道' },
                    { id: '371523002', name: '信发街道' },
                    { id: '371523003', name: '温陈街道' },
                    { id: '371523100', name: '乐平铺镇' },
                    { id: '371523101', name: '冯官屯镇' },
                    { id: '371523103', name: '菜屯镇' },
                    { id: '371523104', name: '博平镇' },
                    { id: '371523105', name: '杜郎口镇' },
                    { id: '371523106', name: '韩屯镇' },
                    { id: '371523107', name: '胡屯镇' },
                    { id: '371523108', name: '肖家庄镇' },
                    { id: '371523109', name: '贾寨镇' },
                    { id: '371523110', name: '洪官屯镇' },
                    { id: '371523208', name: '杨官屯乡' }
                  ]
                },
                {
                  id: '371524',
                  name: '东阿县',
                  children: [
                    { id: '371524001', name: '铜城街道' },
                    { id: '371524002', name: '新城街道' },
                    { id: '371524101', name: '刘集镇' },
                    { id: '371524102', name: '牛角店镇' },
                    { id: '371524103', name: '大桥镇' },
                    { id: '371524104', name: '高集镇' },
                    { id: '371524105', name: '姜楼镇' },
                    { id: '371524107', name: '姚寨镇' },
                    { id: '371524108', name: '鱼山镇' },
                    { id: '371524109', name: '陈集镇' }
                  ]
                },
                {
                  id: '371525',
                  name: '冠县',
                  children: [
                    { id: '371525001', name: '清泉街道' },
                    { id: '371525002', name: '崇文街道' },
                    { id: '371525003', name: '烟庄街道' },
                    { id: '371525101', name: '贾镇' },
                    { id: '371525102', name: '桑阿镇' },
                    { id: '371525103', name: '柳林镇' },
                    { id: '371525104', name: '清水镇' },
                    { id: '371525105', name: '东古城镇' },
                    { id: '371525106', name: '北馆陶镇' },
                    { id: '371525107', name: '店子镇' },
                    { id: '371525108', name: '定远寨镇' },
                    { id: '371525109', name: '辛集镇' },
                    { id: '371525110', name: '梁堂镇' },
                    { id: '371525111', name: '范寨镇' },
                    { id: '371525112', name: '甘官屯镇' },
                    { id: '371525200', name: '斜店乡' },
                    { id: '371525206', name: '兰沃乡' },
                    { id: '371525209', name: '万善乡' }
                  ]
                },
                {
                  id: '371526',
                  name: '高唐县',
                  children: [
                    { id: '371526001', name: '鱼邱湖街道' },
                    { id: '371526002', name: '汇鑫街道' },
                    { id: '371526003', name: '人和街道' },
                    { id: '371526101', name: '梁村镇' },
                    { id: '371526102', name: '尹集镇' },
                    { id: '371526103', name: '清平镇' },
                    { id: '371526104', name: '固河镇' },
                    { id: '371526105', name: '三十里铺镇' },
                    { id: '371526106', name: '琉璃寺镇' },
                    { id: '371526107', name: '赵寨子镇' },
                    { id: '371526108', name: '姜店镇' },
                    { id: '371526109', name: '杨屯镇' }
                  ]
                },
                {
                  id: '371581',
                  name: '临清市',
                  children: [
                    { id: '371581001', name: '青年路街道' },
                    { id: '371581002', name: '新华路街道' },
                    { id: '371581003', name: '先锋路街道' },
                    { id: '371581004', name: '大辛庄街道' },
                    { id: '371581101', name: '松林镇' },
                    { id: '371581102', name: '老赵庄镇' },
                    { id: '371581103', name: '康庄镇' },
                    { id: '371581104', name: '魏湾镇' },
                    { id: '371581105', name: '刘垓子镇' },
                    { id: '371581107', name: '八岔路镇' },
                    { id: '371581108', name: '潘庄镇' },
                    { id: '371581109', name: '烟店镇' },
                    { id: '371581110', name: '唐园镇' },
                    { id: '371581111', name: '金郝庄镇' },
                    { id: '371581112', name: '戴湾镇' },
                    { id: '371581113', name: '尚店镇' }
                  ]
                },
                {
                  id: '371582',
                  name: '开发区',
                  children: [
                    { id: '1935956925770436609', name: '东城街道' },
                    { id: '1935957452377886722', name: '蒋官屯街道' },
                    { id: '1935957575807864834', name: '北城街道' }
                  ]
                },
                {
                  id: '371583',
                  name: '高新区',
                  children: []
                },
                {
                  id: '371584',
                  name: '度假区',
                  children: []
                }
              ]
            }
          ],
          activeIndex: 0,
          CityData: {}
        },
        mounted() {
          this.getAreaTree()
        },
        methods: {
          getAreaTree() {
            var list = this.areaTree
            this.areaTree = list[0].children.map(item => {
              return { text: item.name, id: item.id, children: this.hadleData(item.children) }
            })
            this.areaTree.unshift({ text: list[0].name, id: '', children: [] })
          },
          hadleData(data) {
            return data.map(item => {
              return { text: item.name, id: item.id, children: item.children }
            })
          },
          clickItem(item) {
            this.CityData.areaName = item.text.substring(0, 3) + '...'
            this.CityData.street = item.id
          },
          clickNav(item) {
            this.CityData = { areaName: this.areaTree[item].text, activeIndex: item, district: this.areaTree[item].id }
          },
          onClickSure() {
            localStorage.setItem('areaName', this.CityData.areaName || '聊城市')
            if (this.CityData.street) {
              localStorage.setItem('areaId', this.CityData.street)
            } else {
              localStorage.setItem('areaId', this.CityData.district || '371500')
            }
            window.location.href = './workstation.html?queryObj=' + JSON.stringify(this.CityData)
          }
        }
      })
    </script>
  </body>
</html>
