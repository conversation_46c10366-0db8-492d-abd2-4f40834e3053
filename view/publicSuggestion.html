<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>公开建议</title>
  <link rel="stylesheet" href="../css/common.css" />
  <style>
    [v-cloak] {
      display: none;
    }

    .body_box {
      background: #f5f6fa;
      padding: 20px;
    }

    .suggest_information {
      border-radius: 8px;
      background-color: #fff;
    }

    .suggest_bg {
      width: 100%;
      height: 124px;
    }

    .suggest_information_bg {
      text-indent: 2em;
      padding: 12px 20px;
      line-height: 24px;
    }

    .suggest_information_text {
      font-weight: 400;
      font-size: 15px;
      color: #333333;
    }

    /* 建议办理概览容器 */
    .overview-container {
      margin-top: 18px;
      padding: 16px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .stats-container {
      margin-bottom: 20px;
    }

    .stat-card {
      padding: 16px 10px;
      border-radius: 4px;
      text-align: center;
      margin: 0 4px;
      position: relative;
    }

    .stat-card:first-child {
      margin-left: 0;
    }

    .stat-card:last-child {
      margin-right: 0;
    }

    .stat-card-blue {
      background: #e8f7ff;
    }

    .stat-card-purple {
      background: #f1f5ff;
    }

    .stat-card-green {
      background: #daf6f2;
    }

    .stat-icon {
      margin-bottom: 8px;
    }

    .icon-placeholder {
      width: 32px;
      height: 32px;
    }

    .stat-label {
      display: block;
      font-size: 12px;
      color: #999999;
      margin-bottom: 6px;
    }

    .stat-number {
      display: block;
      font-size: 20px;
    }

    .stat-number-blue {
      color: #308fff;
    }

    .stat-number-purple {
      color: #3a61cd;
    }

    .stat-number-green {
      color: #57bcaa;
    }

    .completion-rate {
      margin-bottom: 10px;
    }

    .rate-header {
      margin-bottom: 8px;
    }

    .rate-label {
      font-size: 14px;
      color: #333333;
    }

    .rate-percentage {
      font-size: 12px;
      color: #104b8b;
    }

    .progress-bar {
      height: 8px;
      background-color: #d1e3f5;
      border-radius: 4px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #1a69bf 0%, #104b8b 100%);
      border-radius: 4px;
      transition: width 0.3s ease;
    }

    /* 全部建议区域 */
    .suggest-content {
      margin-top: 20px;
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    }

    .tab-container {
      margin-bottom: 15px;
    }

    .tab-item {
      padding: 16px 0;
      text-align: center;
      position: relative;
      transition: all 0.3s ease;
      background: linear-gradient(180deg, #d5daeb 0%, #e6e9fa 100%);
    }

    .tab-text {
      font-size: 16px;
      color: #666666;
      font-weight: 400;
      position: relative;
      z-index: 2;
    }

    .tab-active {
      background: #fff;
    }

    .tab-active .tab-text {
      color: #333333;
      font-weight: 600;
    }

    .tab-underline {
      position: absolute;
      bottom: 5px;
      left: 50%;
      transform: translateX(-50%);
      width: 25px;
      height: 3px;
      background-color: #1890ff;
      border-radius: 1.5px;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .tab-active .tab-underline {
      opacity: 1;
    }

    .tab-item:not(:last-child)::after {
      content: '';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1px;
      height: 20px;
      background-color: #e8e8e8;
    }

    .search-container {
      padding: 0 16px;
    }

    .search-box {
      background: #f4f6f8;
      border-radius: 24px;
      padding: 0 10px;
      height: 35px;
      border: 1px solid #e8e8e8;
    }

    .search-icon {
      width: 15px;
      height: 15px;
      margin-right: 10px;
    }

    .search-input {
      font-size: 14px;
      color: #333333;
      height: 100%;
      background-color: transparent;
      border: none;
      outline: none;
    }

    .search-input::placeholder {
      color: #a7b3bf;
      font-size: 14px;
    }

    .search-button {
      padding: 0 10px;
    }

    .search-button-text {
      font-size: 14px;
      color: rgb(117, 117, 117);
      font-weight: 400;
    }

    /* 建议列表 */
    .suggestion-list {
      padding: 0 16px 16px;
    }

    .suggestion-item {
      padding: 16px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .suggestion-item:last-child {
      border-bottom: none;
    }

    .suggestion-title {
      font-size: 16px;
      color: #333333;
      font-weight: 500;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .suggestion-author {
      font-size: 14px;
      color: #666666;
    }

    .suggestion-date {
      font-size: 14px;
      color: #999999;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 简介 -->
    <div class="suggest_information" v-cloak>
      <img src="../img/suggest_top_bg.png" alt="" class="suggest_bg" />
      <div class="suggest_information_bg">
        <span class="suggest_information_text" v-cloak>{{information}}</span>
      </div>
    </div>

    <!-- 建议办理概览 -->
    <div class="overview-container" v-cloak>
      <div class="section-header flex_box flex_align_center">
        <div class="section-line"></div>
        <span class="section-title">建议办理概览</span>
      </div>
      <!-- 统计卡片 -->
      <div class="stats-container flex_box flex_justify_between">
        <div class="flex_1 stat-card stat-card-blue">
          <div class="stat-icon">
            <img src="../img/icon_suggest_total.png" alt="" class="icon-placeholder" />
          </div>
          <span class="stat-label">建议总数</span>
          <span class="stat-number stat-number-blue" v-cloak>{{totalSuggestions}}</span>
        </div>

        <div class="flex_1 stat-card stat-card-purple">
          <div class="stat-icon">
            <img src="../img/icon_completed.png" alt="" class="icon-placeholder" />
          </div>
          <span class="stat-label">已办结</span>
          <span class="stat-number stat-number-purple" v-cloak>{{completedSuggestions}}</span>
        </div>

        <div class="flex_1 stat-card stat-card-green">
          <div class="stat-icon">
            <img src="../img/icon_process.png" alt="" class="icon-placeholder" />
          </div>
          <span class="stat-label">正在办理</span>
          <span class="stat-number stat-number-green" v-cloak>{{processingSuggestions}}</span>
        </div>
      </div>
      <!-- 办结率 -->
      <div class="completion-rate">
        <div class="rate-header flex_box flex_align_center flex_justify_between">
          <span class="rate-label">办结率</span>
          <span class="rate-percentage" v-cloak>{{completionRate}}</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" :style="`width: ${completionRate}`"></div>
        </div>
      </div>
    </div>

    <!-- 全部建议 -->
    <div class="suggest-content" v-cloak>
      <!-- 标签页 -->
      <div class="tab-container flex_box">
        <!-- <div class="tab-item tab-active" data-tab="latest">
          <span class="tab-text">最新建议</span>
          <div class="tab-underline"></div>
        </div> -->
        <div class="flex_1 tab-item tab-active" data-tab="all">
          <span class="tab-text">全部建议</span>
          <div class="tab-underline"></div>
        </div>
      </div>
      <!-- 搜索框 -->
      <div class="search-container">
        <div class="search-box flex_box flex_align_center">
          <img class="search-icon" src="../img/search.png" alt="" />
          <input class="search-input flex_1" placeholder="搜索建议关键词、代表姓名等" />
          <div class="search-button">
            <span class="search-button-text">｜ 搜索</span>
          </div>
        </div>
      </div>
      <!-- 建议列表 -->
      <div class="suggestion-list">
        <div class="suggestion-item" v-for="(item, index) in suggestionList" :key="index"
          @click="openSuggestDetails(item.id)">
          <div class="suggestion-title" v-cloak>{{item.title}}</div>
          <div class="flex_box flex_justify_between flex_align_center">
            <span class="suggestion-author" v-cloak>建议者：{{item.suggestUserName || item.createBy}}</span>
            <span class="suggestion-date" v-cloak>{{formatTime(item.submitDate)}}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicSuggestCountKey = '04987b0d9ce82f31ae925a0647efb07938d4231c8239c9ac3396e9d1d1accf581111b7c23e00d2ebcfe61343bcf042c732555badb61a7de90594e09aa385cdf16c' // 获取建议统计公钥
    var publicSuggestListKey = '04330592d66881a0acbaf181e69d55cd79a9bd52d3557021c8141de6ae416ae0dd8a383ca3d6dd2ff3cda09ba432b822ded349b7bd7e181fe6c60b30851dc3a147' // 获取建议列表公钥
    var app = new Vue({
      el: '#app',
      data: {
        information: '建议是人民代表大会制度的重要组成部分，是代表履行职责、反映民意、参与地方治理的重要方式。聊城市人大常委会依法公开代表建议及其办理情况，接受社会监督。',
        // 统计数据
        totalSuggestions: 0,
        completedSuggestions: 0,
        processingSuggestions: 0,
        completionRate: 0,
        // 标签页状态
        activeTab: 'all', // 'latest' 或 'all'
        // 搜索关键词
        searchKeyword: '',
        // 建议列表
        suggestionList: []
      },
      async mounted () {
        var vConsole = new VConsole()
        this.getSuggestCount()
        this.getSuggestList()
      },
      methods: {
        // 获取建议统计
        getSuggestCount () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'suggestionCount'
          var interfacecontent = {}
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicSuggestCountKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.totalSuggestions = ret.data.totalCount || 0
              that.completedSuggestions = ret.data.overCount || 0
              that.processingSuggestions = ret.data.handleCount || 0
              that.completionRate = ret.data.overRate || 0
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getSuggestCount()
            }
          })
        },

        // 获取建议列表
        getSuggestList () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'suggestionList'
          var interfacecontent = {
            countItemCode: 'all',
            isAnd: 1,
            keyword: this.searchKeyword,
            orderBys: [{ columnId: "id_sgsn_suggestion_stream_number", isDesc: "1" }],
            pageNo: 1,
            pageSize: 999,
            tableId: 'id_sgsn_suggestion'
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicSuggestListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            console.log('获取建议列表res==>>', res)
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取建议列表ret==>>', ret)
              that.suggestionList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getSuggestList()
            }
          })
        },

        // 打开建议详情
        openSuggestDetails (id) {
          window.location.href = './publicSuggestionDetails.html'
        }
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>