<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>我的留言</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none
    }

    .body_box {
      padding-bottom: 20px;
      background-color: #F4F6F8;
      height: 100vh;
    }

    .search_box {
      background-color: #ffffff;
    }

    .message_list {
      margin: 10px;
    }

    .message_item {
      background-color: #fff;
      padding: 15px;
      margin-bottom: 10px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .status_node_no {
      padding: 4px 10px;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      color: rgb(246, 147, 28);
      background: rgba(246, 147, 28, 0.1);
    }

    .status_node_evaluate {
      padding: 4px 10px;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      color: rgb(43, 189, 75);
      background: rgba(43, 189, 75, 0.1);
    }

    .status_pending {
      padding: 4px 10px;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      color: rgb(230, 162, 60);
      background: rgba(230, 162, 60, 0.1);
    }

    .status_no {
      padding: 4px 10px;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      color: rgb(191, 34, 34);
      background: rgba(191, 34, 34, 0.1);
    }

    .message_title {
      font-size: 17px;
      font-weight: 600;
      color: #333;
      margin-top: 10px;
      line-height: 24px;
    }

    .message_target {
      font-size: 14px;
      color: #666;
      margin-top: 10px;
    }

    .message_name_time {
      margin-bottom: 10px;
      font-size: 14px;
      color: #666;
      margin-top: 10px;
    }

    .message_time {
      margin-left: 30px;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <van-tabs v-model="active" color="#3894FF" title-active-color="#1989fa" line-height="3">
      <van-tab title="所有" v-cloak>
        <div class="search_box">
          <van-search v-model="searchKeywords" placeholder="请输入关键词" />
        </div>
        <div class="message_list">
          <div v-for="message in filteredMessages" :key="message.id" class="message_item"
            @click="viewMessageDetail(message.id)">
            <div class="flex_box">
              <div
                :class="message.letterStatus=='未回复'?'status_node_no':message.letterStatus=='已评价'?'status_node_evaluate':message.letterStatus=='已评价'?'':''">
                {{message.letterStatus}}
              </div>
              <div style="margin-left: 20px;"
                :class="message.checkStatus==0?'status_pending':message.checkStatus==2?'status_no':''">
                {{message.checkStatus==0?'待审核':message.checkStatus==2?'审核不通过':''}}
              </div>
            </div>
            <div class="message_title">{{ message.title }}</div>
            <div class="message_target">反映对象：{{ message.receiverId||message.stationId||'暂无' }}</div>
            <div class="message_name_time flex_box flex_align_center">
              <div class="message_name">{{ message.senderUserName }}</div>
              <div class="message_time">{{ formatTime(message.receiveTime) }}</div>
            </div>
          </div>
          <div v-if="filteredMessages.length === 0" style="text-align: center; padding: 20px; color: #999;">
            暂无留言记录
          </div>
        </div>
      </van-tab>
      <van-tab title="未回复" v-cloak>
        <div class="search_box">
          <van-search v-model="searchKeywords" placeholder="请输入关键词" />
        </div>
        <div class="message_list">
          <div v-for="message in filteredMessages" :key="message.id" class="message_item"
            @click="viewMessageDetail(message.id)">
            <div class="message_status">
              <span class="status_tag status_pending">未回复</span>
              <span v-if="message.subStatus" :class="{
                'status_tag status_reviewing': message.subStatus === 'reviewing',
                'status_tag status_rejected': message.subStatus === 'rejected'
              }">{{ message.subStatus === 'reviewing' ? '待审核' : '审核不通过' }}</span>
            </div>
            <div class="message_title">{{ message.title }}</div>
            <div class="message_header">
              <div class="message_target">反映对象：{{ message.target }}</div>
              <div class="message_date">{{ message.date }}</div>
            </div>
          </div>
          <div v-if="filteredMessages.length === 0" style="text-align: center; padding: 20px; color: #999;">
            暂无未回复留言
          </div>
        </div>
      </van-tab>
      <van-tab title="已回复" v-cloak>
        <div class="search_box">
          <van-search v-model="searchKeywords" placeholder="请输入关键词" />
        </div>
        <div class="message_list">
          <div v-for="message in filteredMessages" :key="message.id" class="message_item"
            @click="viewMessageDetail(message.id)">
            <div class="message_status">
              <span class="status_tag status_replied">已回复</span>
            </div>
            <div class="message_title">{{ message.title }}</div>
            <div class="message_header">
              <div class="message_target">反映对象：{{ message.target }}</div>
              <div class="message_date">{{ message.date }}</div>
            </div>
          </div>
          <div v-if="filteredMessages.length === 0" style="text-align: center; padding: 20px; color: #999;">
            暂无已回复留言
          </div>
        </div>
      </van-tab>
      <van-tab title="已评价" v-cloak>
        <div class="search_box">
          <van-search v-model="searchKeywords" placeholder="请输入关键词" />
        </div>
        <div class="message_list">
          <div v-for="message in filteredMessages" :key="message.id" class="message_item"
            @click="viewMessageDetail(message.id)">
            <div class="message_status">
              <span class="status_tag status_evaluated">已评价</span>
            </div>
            <div class="message_title">{{ message.title }}</div>
            <div class="message_header">
              <div class="message_target">反映对象：{{ message.target }}</div>
              <div class="message_date">{{ message.date }}</div>
            </div>
          </div>
          <div v-if="filteredMessages.length === 0" style="text-align: center; padding: 20px; color: #999;">
            暂无已评价留言
          </div>
        </div>
      </van-tab>
    </van-tabs>
  </div>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    // 获取我的留言私钥
    var areaKey = '00871c7d56551fb20bde67a129649e7faf95c05d9203d47ed6de529df5722303b4'
    // 获取我的留言公钥
    var publicKey = '045b4e75032df41d536233631f426ef247ac738049ab291d1612df02801cb1f37cf9ef7949e5f00cecdb55411b3822deef5b03b8ab48fa8b45ebc546d1d88c32f5'

    var app = new Vue({
      el: "#app",
      data: {
        active: 0,
        searchKeywords: '',
        messageList: []
      },
      mounted () {
        this.initMockData()
      },
      methods: {
        initMockData () {
          this.messageList = [{ "id": "1982824730750640130", "stationId": "莘县朝城人大代表工作室", "receiverId": "", "receiverMobile": null, "recorder": "Geng", "recorderMobile": "***********", "receiverType": 1, "stationLetterType": { "value": "jy", "label": "教育", "name": "教育", "dictCode": "station_letter_type" }, "stationLetterClass": { "value": null, "label": null, "name": null, "dictCode": null }, "title": "1", "content": "2", "pictures": "", "attachmentIds": null, "receiveTime": 1761577229352, "senderId": "o9RxP1wJqkzomDnvOg1fxlaMdUFs", "senderUserName": "1", "senderMobile": "***********", "senderEmail": null, "senderAddr": "1", "senderType": null, "hasAnswer": 0, "answerTime": null, "stationAssignType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasComplet": 0, "hasEvaluate": null, "evaluateTime": null, "checkStatus": 0, "createBy": null, "createDate": 1761577229571, "terminalName": null, "businessId": null, "businessCode": null, "city": null, "country": null, "town": null, "letterStatus": "未回复", "evaluateStatus": 1, "myEvaluateStatus": 1, "showCheckStatus": 1 }, { "id": "1982817886531510273", "stationId": "", "receiverId": "谢东华", "receiverMobile": "***********", "recorder": "Geng", "recorderMobile": "***********", "receiverType": 2, "stationLetterType": { "value": "sh", "label": "社会", "name": "社会", "dictCode": "station_letter_type" }, "stationLetterClass": { "value": null, "label": null, "name": null, "dictCode": null }, "title": "测试个人002", "content": "测试个人内容002", "pictures": "1ea52c57-2490-42e8-b64e-46e5d7ab514c.png,f3612d1e-beb2-43cc-9769-11c7188ab474.png", "attachmentIds": null, "receiveTime": 1761575597503, "senderId": "o9RxP1wJqkzomDnvOg1fxlaMdUFs", "senderUserName": "张光", "senderMobile": "***********", "senderEmail": null, "senderAddr": "测试地址", "senderType": null, "hasAnswer": 0, "answerTime": null, "stationAssignType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasComplet": 0, "hasEvaluate": null, "evaluateTime": null, "checkStatus": 2, "createBy": null, "createDate": 1761575597782, "terminalName": null, "businessId": null, "businessCode": null, "city": null, "country": null, "town": null, "letterStatus": "未回复", "evaluateStatus": 1, "myEvaluateStatus": 1, "showCheckStatus": 1 }, { "id": "1982816129961189378", "stationId": "", "receiverId": "", "receiverMobile": "***********", "recorder": "Geng", "recorderMobile": "***********", "receiverType": 2, "stationLetterType": { "value": "jy", "label": "教育", "name": "教育", "dictCode": "station_letter_type" }, "stationLetterClass": { "value": null, "label": null, "name": null, "dictCode": null }, "title": "测试个人001", "content": "测试个人内容001", "pictures": "58da0399-69b9-4f80-a397-d47dd2e05104.png", "attachmentIds": null, "receiveTime": 1761575178632, "senderId": "o9RxP1wJqkzomDnvOg1fxlaMdUFs", "senderUserName": "计算技", "senderMobile": "***********", "senderEmail": null, "senderAddr": "地址11", "senderType": null, "hasAnswer": 0, "answerTime": null, "stationAssignType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasComplet": 0, "hasEvaluate": null, "evaluateTime": null, "checkStatus": 1, "createBy": null, "createDate": 1761575178983, "terminalName": null, "businessId": null, "businessCode": null, "city": null, "country": null, "town": null, "letterStatus": "未回复", "evaluateStatus": 1, "myEvaluateStatus": 1, "showCheckStatus": 1 }, { "id": "1982809503300608001", "stationId": "莘县朝城人大代表工作室", "receiverId": "", "receiverMobile": null, "recorder": "Geng", "recorderMobile": "***********", "receiverType": 1, "stationLetterType": { "value": "stjs", "label": "生态建设", "name": "生态建设", "dictCode": "station_letter_type" }, "stationLetterClass": { "value": null, "label": null, "name": null, "dictCode": null }, "title": "测试007", "content": "测试内容007", "pictures": "507ed49f-e4aa-4764-b71b-1d13a5f45081.png,34b90bb8-3bb6-48a0-90a2-f7a665fa4412.png", "attachmentIds": null, "receiveTime": 1761573598660, "senderId": "o9RxP1wJqkzomDnvOg1fxlaMdUFs", "senderUserName": "程慧", "senderMobile": "***********", "senderEmail": null, "senderAddr": "地址007", "senderType": null, "hasAnswer": 0, "answerTime": null, "stationAssignType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasComplet": 0, "hasEvaluate": "满意", "evaluateTime": 1761642347318, "checkStatus": 1, "createBy": null, "createDate": 1761573599063, "terminalName": null, "businessId": null, "businessCode": null, "city": null, "country": null, "town": null, "letterStatus": "已评价", "evaluateStatus": 1, "myEvaluateStatus": 1, "showCheckStatus": 1 }, { "id": "1982809023509979138", "stationId": "莘县朝城人大代表工作室", "receiverId": "", "receiverMobile": null, "recorder": "Geng", "recorderMobile": "***********", "receiverType": 1, "stationLetterType": { "value": "cjnl", "label": "财经农林", "name": "财经农林", "dictCode": "station_letter_type" }, "stationLetterClass": { "value": null, "label": null, "name": null, "dictCode": null }, "title": "测试009", "content": "测试内容", "pictures": "70b10357-9582-4f9c-a2cb-b4bab6ef9d24,56de925e-dde5-4a03-bd47-a9b71ac5efed", "attachmentIds": null, "receiveTime": 1761573484308, "senderId": "o9RxP1wJqkzomDnvOg1fxlaMdUFs", "senderUserName": "李思思", "senderMobile": "***********", "senderEmail": null, "senderAddr": "地址1", "senderType": null, "hasAnswer": 0, "answerTime": null, "stationAssignType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasComplet": 0, "hasEvaluate": null, "evaluateTime": null, "checkStatus": 1, "createBy": null, "createDate": 1761573484673, "terminalName": null, "businessId": null, "businessCode": null, "city": null, "country": null, "town": null, "letterStatus": "未回复", "evaluateStatus": 1, "myEvaluateStatus": 1, "showCheckStatus": 1 }, { "id": "1982807621274460162", "stationId": "莘县朝城人大代表工作室", "receiverId": "", "receiverMobile": null, "recorder": "Geng", "recorderMobile": "***********", "receiverType": 1, "stationLetterType": { "value": "jy", "label": "教育", "name": "教育", "dictCode": "station_letter_type" }, "stationLetterClass": { "value": null, "label": null, "name": null, "dictCode": null }, "title": "测试008", "content": "测试内容", "pictures": "18edf4a1-057a-411b-be7b-ccded00fe834", "attachmentIds": null, "receiveTime": 1761573150037, "senderId": "o9RxP1wJqkzomDnvOg1fxlaMdUFs", "senderUserName": "张叔", "senderMobile": "***********", "senderEmail": null, "senderAddr": "1地址", "senderType": null, "hasAnswer": 0, "answerTime": null, "stationAssignType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasComplet": 0, "hasEvaluate": null, "evaluateTime": null, "checkStatus": 1, "createBy": null, "createDate": 1761573150353, "terminalName": null, "businessId": null, "businessCode": null, "city": null, "country": null, "town": null, "letterStatus": "未回复", "evaluateStatus": 1, "myEvaluateStatus": 1, "showCheckStatus": 1 }]
        },

        getFilteredMessages () {
          let filtered = this.messageList
          if (this.searchKeywords) {
            filtered = filtered.filter(msg =>
              msg.title.includes(this.searchKeywords) ||
              msg.target.includes(this.searchKeywords)
            )
          }

          // 根据当前标签页状态过滤
          switch (this.active) {
            case 0: // 所有
              return filtered
            case 1: // 未回复
              return filtered.filter(msg => msg.status === 'pending')
            case 2: // 已回复
              return filtered.filter(msg => msg.status === 'replied')
            case 3: // 已评价
              return filtered.filter(msg => msg.status === 'evaluated')
            default:
              return filtered
          }
        },

        onTabChange () {
          console.log('切换到标签页:', this.active)
        },

        viewMessageDetail (messageId) {
          window.location.href = './publicOpinionDetails.html?id=' + messageId
        }
      },
      watch: {
        active () {
          this.onTabChange()
        }
      },

      // 计算属性：根据当前状态过滤消息
      computed: {
        filteredMessages () {
          return this.getFilteredMessages();
        }
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>