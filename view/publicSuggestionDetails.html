<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>详情</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      padding: 20px;
    }

    .suggestion-title {
      width: 100%;
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
    }

    .suggestion-author {
      font-size: 14px;
      color: #666666;
    }

    .suggestion-date {
      font-size: 14px;
      color: #999999;
    }

    .suggestion-content {
      width: 100%;
    }

    .suggestion-content {
      width: 100%;
      font-weight: 400;
      font-size: 16px;
      color: #333;
      line-height: 28px;
      margin-top: 12px;
    }

    .suggestion-content img {
      width: 350px !important;
      height: auto !important;
      vertical-align: middle;
      margin: 10px auto !important;
      display: block !important;
    }

    .suggestion-content p {
      font-size: 16px !important;
      background-color: #fff !important;
      margin-top: 2.5vw !important;
    }

    .suggestion-content img {
      max-width: 360px !important;
      height: auto;
      vertical-align: middle;
    }

    .suggestion-content .img {
      width: 100%;
      height: 229px;
      margin-top: 10px;
    }

    .suggestion-content .img img {
      width: 100%;
      height: 100%;
      vertical-align: middle;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <div class="suggestion-title" v-cloak>{{details.title}}</div>
    <div class="flex_box flex_justify_between flex_align_center">
      <span class="suggestion-author" v-cloak>建议者：{{details.suggestUserName || details.createBy}}</span>
      <span class="suggestion-date" v-cloak>{{formatTime(details.submitDate)}}</span>
    </div>
    <div class="suggestion-content" v-html="details.content"></div>
  </div>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.20.1/moment.min.js"></script>
  <script>
    var privateKey = '4e2524722f0b7de6bc9a49b4e955a16809c0a8630d8b3b38388bc15e2c67bdc8' // 私钥
    var publicActivityInfoKey = '04f29743b93744f5c16de8071974be322ae50025d900de52866919ddfe299dd58f27d718307ccc5f4f2d78b4e6953d7e3568e639b039b0736ecccabad67c64055f' // 获取活动动态详情公钥
    var id = ''
    var app = new Vue({
      el: '#app',
      data: {
        details: {
          title: '关于支持东阿县开展水系连通及农村水系综合整治试点工作的建议',
          suggestUserName: '李恒道',
          submitDate: 1736478777000,
          content:
            '<p>&nbsp; &nbsp; &nbsp; &nbsp; 6月2日上午，为持续推进“三基”工程落实，市人大代表、青岛商务学校正高级讲师徐璟和区人大代表、青岛粥全粥到伟业酒店管理有限公司董事长李梦飞在团岛社区参加“三基”工程社区联席会议，认真听取社区近期重点工作推进情况，同社区各级负责同志交流意见，并就“人大代表听民声 点亮居民微心愿”主题活动商议具体方案，进一步关爱并帮助辖区内的老人和孩子，推动辖区内一老一小真正感受到人大代表的温暖和关爱。</p><p>&nbsp; &nbsp; &nbsp; &nbsp;会议结束后，两位代表在社区接待居民，认真听取关于老旧楼院改造、辖区老人业余爱好拓展缺乏资源等问题的意见和建议，认真记录并交流解决措施，积极发挥人大代表作用。</p><p style="line-height: 2em;"><br/></p>'
        }
      },
      async mounted () {
        // var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        if (id) {
          this.getInfo()
        }
      },
      methods: {
        // 获取人大动态详情
        getInfo () {
          var that = this
        }
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>