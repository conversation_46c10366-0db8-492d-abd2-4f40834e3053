<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>详情</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      padding: 20px;
    }

    .suggestion-title {
      width: 100%;
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
    }

    .suggestion-author {
      font-size: 14px;
      color: #666666;
    }

    .suggestion-date {
      font-size: 14px;
      color: #999999;
    }

    .suggestion-content {
      width: 100%;
    }

    .suggestion-content {
      width: 100%;
      font-weight: 400;
      font-size: 16px;
      color: #333;
      line-height: 28px;
      margin-top: 12px;
    }

    .suggestion-content img {
      width: 350px !important;
      height: auto !important;
      vertical-align: middle;
      margin: 10px auto !important;
      display: block !important;
    }

    .suggestion-content p {
      font-size: 16px !important;
      background-color: #fff !important;
      margin-top: 2.5vw !important;
    }

    .suggestion-content img {
      max-width: 360px !important;
      height: auto;
      vertical-align: middle;
    }

    .suggestion-content .img {
      width: 100%;
      height: 229px;
      margin-top: 10px;
    }

    .suggestion-content .img img {
      width: 100%;
      height: 100%;
      vertical-align: middle;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <div class="suggestion-title" v-cloak>{{details.title}}</div>
    <div class="flex_box flex_justify_between flex_align_center">
      <span class="suggestion-author" v-cloak>建议者：{{details.suggestUserName || details.createBy}}</span>
      <span class="suggestion-date" v-cloak>{{formatTime(details.submitDate)}}</span>
    </div>
    <div class="suggestion-content" v-html="details.content"></div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicSuggestInfoKey = '04ccb15f7ec18dc78323bafbf3b430a005b55d3c55d616f1c79f7fe362a1a12ca0afcc636a135e8d8e02be8e2f13fd088f79a4263027f602d3890505f63e882fe8' // 获取建议详情公钥
    var id = ''
    var app = new Vue({
      el: '#app',
      data: {
        details: {}
      },
      async mounted () {
        // var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        if (id) {
          this.getInfo()
        }
      },
      methods: {
        // 获取建议详情
        getInfo () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'suggestionInfo'
          var interfacecontent = { detailId: id }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicSuggestInfoKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取建议详情====>>', ret)
              that.details = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getSuggestCount()
            }
          })
        }
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>