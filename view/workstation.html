<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>代表工作站</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      background: #f5f6fa;
      padding: 20px;
      height: 100vh;
    }

    .workstation_information {
      border-radius: 8px;
      background-color: #fff;
    }

    .workstation_bg {
      width: 100%;
      height: 124px;
    }

    .workstation_information_bg {
      text-indent: 2em;
      padding: 12px 20px;
      line-height: 24px;
    }

    .workstation_information_text {
      font-weight: 400;
      font-size: 15px;
      color: #333333;
    }

    /* 代表工作概览容器 */
    .overview_container {
      margin-top: 18px;
      padding: 16px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .stats-container {
      margin-bottom: 20px;
    }

    .stat-card {
      padding: 16px 10px;
      border-radius: 4px;
      text-align: center;
      margin: 0 4px;
      position: relative;
    }

    .stat-card:first-child {
      margin-left: 0;
    }

    .stat-card:last-child {
      margin-right: 0;
    }

    .stat-card-blue {
      background: #F1F5FF;
    }

    .stat-card-purple {
      background: #DAF6F2;
    }

    .stat-card-green {
      background: #F9EEDA;
    }

    .stat-icon {
      margin-bottom: 8px;
    }

    .icon-placeholder {
      width: 32px;
      height: 32px;
    }

    .stat-label {
      display: block;
      font-size: 12px;
      color: #999999;
      margin-bottom: 6px;
    }

    .stat-number {
      display: block;
      font-size: 20px;
    }

    .stat-number-blue {
      color: #308fff;
    }

    .stat-number-purple {
      color: #3a61cd;
    }

    .stat-number-green {
      color: #57bcaa;
    }

    .completion-rate {
      margin-bottom: 10px;
    }

    .rate-header {
      margin-bottom: 8px;
    }

    .rate-label {
      font-size: 14px;
      color: #333333;
    }

    .rate-percentage {
      font-size: 12px;
      color: #104b8b;
    }

    .progress-bar {
      height: 8px;
      background-color: #d1e3f5;
      border-radius: 4px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #1a69bf 0%, #104b8b 100%);
      border-radius: 4px;
      transition: width 0.3s ease;
    }

    /* 所有站点 */
    .workstation_container {
      margin-top: 18px;
      padding: 0 16px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .custom-search-container {
      padding: 15px 0;
    }

    .search-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #f4f6f8;
      border-radius: 16px;
      height: 36px;
      padding: 0 10px;
      position: relative;
    }

    .area-selector {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: #333;
      font-size: 14px;
      width: 70px;
    }

    .city-name {
      margin-right: 4px;
      font-weight: 500;
      font-size: 14px;
    }

    .area-arrow {
      width: 12px;
      height: 12px;
      transform: rotate(90deg);
    }

    .search-input {
      flex: 1;
      border: none;
      background: transparent;
      outline: none;
      font-size: 14px;
      color: #333;
      padding-left: 24px;
    }

    .search-input::placeholder {
      color: #999;
    }

    /* .search-icon {
      position: absolute;
      left: 80px;
      width: 16px;
      height: 16px;
    } */

    .search-text {
      color: #0D75FF;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      margin-left: 10px;
      width: 32px;
    }

    .workstation_item {
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: flex-start;
    }

    .workstation_img {
      position: relative;
      margin-right: 10px;
    }

    .workstation_img_url {
      border-radius: 4px;
      width: 132px;
      height: 100px;
    }

    .workstation_img_top {
      position: absolute;
      z-index: 1;
      left: 0px;
      top: 0px;
      border-radius: 10px 0;
      padding: 2px 10px;
      background: linear-gradient(270deg, #3894FF 0%, #41BBFF 100%);
    }

    .workstation_img_top_text {
      color: rgb(255, 255, 255);
      font-size: 12px;
    }

    .workstation_item_title {
      font-size: 16px;
      color: #333333;
      display: flex;
      margin-bottom: 3px;
    }

    .icon_box {
      width: 16px;
      height: 16px;
      margin-top: 2px;
      margin-right: 2px;
    }

    .text_box {
      font-size: 12px;
      color: #696969;
      flex: 1;
    }
  </style>
</head>

<link>
<div class="body_box" id="app">
  <!-- 简介 -->
  <div class="workstation_information">
    <img src="../img/workStation_top_bg.png" alt="" class="workstation_bg" />
    <div class="workstation_information_bg">
      <span class="workstation_information_text" v-cloak>{{information}}</span>
    </div>
  </div>
  <!-- 代表工作概览 -->
  <div class="overview_container">
    <div class="section-header flex_box flex_align_center">
      <div class="section-line"></div>
      <span class="section-title">代表工作概览</span>
    </div>
    <!-- 统计卡片 -->
    <div class="stats-container flex_box flex_justify_between">
      <div class="flex_1 stat-card stat-card-blue" v-cloak>
        <div class="stat-icon">
          <img src="../img/icon_work_station_total.png" alt="" class="icon-placeholder" />
        </div>
        <span class="stat-label">工作站总数</span>
        <span class="stat-number stat-number-blue" v-cloak>{{workStationTotal}}</span>
      </div>
      <div class="flex_1 stat-card stat-card-purple" v-cloak>
        <div class="stat-icon">
          <img src="../img/icon_representative_count.png" alt="" class="icon-placeholder" />
        </div>
        <span class="stat-label">在站代表</span>
        <span class="stat-number stat-number-purple" v-cloak>{{representativeCount}}</span>
      </div>
      <div class="flex_1 stat-card stat-card-green" v-cloak>
        <div class="stat-icon">
          <img src="../img/icon_annual_visitor_count.png" alt="" class="icon-placeholder" />
        </div>
        <span class="stat-label">年接待量</span>
        <span class="stat-number stat-number-green" v-cloak>{{annualVisitorCount}}</span>
      </div>
    </div>
    <!-- 满意度评价 -->
    <div class="completion-rate">
      <div class="rate-header flex_box flex_align_center flex_justify_between">
        <span class="rate-label">满意度评价</span>
        <span class="rate-percentage" v-cloak>{{completionRate}}</span>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" :style="`width: ${completionRate}`"></div>
      </div>
    </div>
  </div>
  <!-- 站点列表 -->
  <div class="workstation_container">
    <van-tabs v-model="active" shrink color="#0D75FF" swipeable>
      <van-tab v-for="(item,index) in activeData" :key="item.id" :name="item.id" :title="item.value">
        <!-- 自定义搜索模块 -->
        <div class="custom-search-container">
          <div class="search-wrapper">
            <!-- 左侧地区选择 -->
            <div class="area-selector" @click="goToCitySelect">
              <span class="city-name" v-cloak>{{areaName}}</span>
              <img src="../img/arrow_right.png" alt="下拉箭头" class="area-arrow" />
            </div>
            <!-- 中间搜索输入框 -->
            <input type="text" v-model="keyword" placeholder="搜索关键词" class="search-input" />
            <!-- <img src="../img/search.png" alt="搜索图标" class="search-icon" /> -->
            <!-- 右侧搜索文本 -->
            <div class="search-text" @click="search">搜索</div>
          </div>
        </div>
        <van-list v-model:loading="loading" :finished="finished" :immediate-check="false" finished-text="没有更多了"
          offset="52" @load="onLoad">
          <div class="workstation_list">
            <template v-if="dataList&&dataList.length!==0">
              <div class="flex_box workstation_item" v-for="item in dataList" :key="item.id" @click="openDetails(item)">
                <div class="workstation_img" v-cloak>
                  <img v-if="item.showImgs" :src="fileImgUrl+item.showImgs" alt="" class="workstation_img_url"
                    v-cloak />
                  <img v-else :src="defaultImg" alt="" class="workstation_img_url" v-cloak />
                  <!-- <div class="workstation_img_top">
                      <div class="workstation_img_top_text">{{item.townName}}</div>
                    </div> -->
                </div>
                <div class="workstation_item_right">
                  <div class="workstation_item_title" v-cloak>{{ item.name }}</div>
                  <div class="flex_box flex_align_center flex_justify_between" style="margin-bottom: 3px;">
                    <div class="flex_box flex_align_center" v-if="item.contactUserName" v-cloak>
                      <img v-cloak src="../img/icon_user.png" alt="" class="icon_box" />
                      <span v-cloak class="text_box">{{item.contactUserName}}</span>
                    </div>
                    <div class="flex_box flex_align_center" v-if="item.contactTelephone" v-cloak>
                      <img src="../img/icon_phone.png" alt="" class="icon_box" />
                      <span class="text_box">{{ item.contactTelephone }}</span>
                    </div>
                  </div>
                  <div class="flex_box flex_align_center" style="margin-bottom: 3px;" v-if="item.openTime" v-cloak>
                    <img src="../img/icon_time.png" alt="" class="icon_box" />
                    <span class="text_box">{{ item.openTime }}</span>
                  </div>
                  <div class="flex_box flex_align_center" v-if="item.address" v-cloak>
                    <img src="../img/icon_location.png" alt="" class="icon_box" />
                    <span class="text_box two_text">{{ item.address }}</span>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </van-list>
      </van-tab>
    </van-tabs>
  </div>
</div>
<script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
<script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
<script defer type="text/javascript" src="../js/aes.js"></script>
<script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
<script src="../js/vue.min.js"></script>
<script src="../js/vant.min.js"></script>

<script>
  var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
  var publicWorkstationCountKey = '0440ef419ec8fd29b8f73a7d2366fed8ae77c2ecfb0933f9c7c888b8f8341bf93739db4e71c41e95aeaf8925f858b881059c99c9e40a8a26c0e1b560475b3ddd33' 	// 获取代表工作站统计公钥
  var publicWorkstationListKey = '04cbc52ea8743ea164311561e8566a734cf0228c03fcd6560694f58a6c2ef7b7803ff6a83aa46d02cf3315a06562df3f92c355a506b8b1a597e819a652c73ea38d' 	// 获取代表工作站列表公钥
  var queryObj = {}
  var app = new Vue({
    el: "#app",
    data: {
      information: '代表工作站是****联系群众、了解民情、收集民意的重要平台，是代表履职尽责的重要阵地，也是密切代表与选民联系的重要桥梁。聊城市现有代表工作站47个，覆盖城乡各区域。',
      // 统计数据
      workStationTotal: 0,
      representativeCount: 0,
      annualVisitorCount: 0,
      completionRate: '0',

      active: '0',
      activeData: [
        { id: '0', value: '所有站点' }
        // { id: '1', value: '关注站点' },
        // { id: '2', value: '我的站点' }
      ],
      keyword: '',
      areaName: '聊城市',
      areaId: '371500',
      loading: true,
      finished: false,
      dataList: [],
      fileImgUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/image/',
      defaultImg: '../img/workstation_default.png',
      flag: true,
      pageNo: 1,
      pageSize: 15,
      scrollTop: 0,
    },
    mounted () {
      // var vConsole = new VConsole() // 初始化
      const urlParams = new URLSearchParams(window.location.search)
      queryObj = JSON.parse(urlParams.get('queryObj'))
      if (queryObj) {
        this.areaName = queryObj.areaName || '聊城市'
        this.areaId = queryObj.district || '371500'
        this.getWorkstationCount()
        this.getWorkstationList()
      } else {
        this.getWorkstationCount()
        this.getWorkstationList()
      }
    },
    methods: {
      // 搜索
      search () {
        this.pageNo = 1
        this.dataList = []
        this.finished = false
        this.getWorkstationList()
      },
      // 下拉加载
      onLoad () {
        if (this.flag) {
          this.getWorkstationList()
        }
      },
      // 获取代表工作站统计
      getWorkstationCount () {
        var that = this
        var appid = 'lcsszrduwgvg'
        var interfaceid = 'workstationCount'
        var interfacecontent = {
          stationOwnerAreaId: this.areaId,
          tableId: 'id_station_worker_station'
        }
        let extraData = {
          header: { 'u-login-areaId': '371500' }
        }
        let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicWorkstationCountKey)
        vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
          try {
            var ret = JSON.parse(SM.decrypt(res, privateKey))
            that.workStationTotal = ret.data.stationCount || 0
            that.representativeCount = ret.data.memberCount || 0
            that.annualVisitorCount = ret.data.letterCount || 0
            that.completionRate = ret.data.evaRate || 0
          } catch (error) {
            // 在这里处理异常情况
            console.log('解析JSON时发生错误：', error)
            that.getWorkstationCount()
          }
        })
      },
      // 获取代表工作站列表
      getWorkstationList () {
        var that = this
        that.flag = false
        var appid = 'lcsszrduwgvg'
        var interfaceid = 'workerStationList'
        var interfacecontent = {
          isRoleFilter: 0,
          keyword: this.keyword,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          stationOwnerAreaId: this.areaId,
          tableId: 'id_station_worker_station'
        }
        let extraData = {
          header: { 'u-login-areaId': '371500' }
        }
        let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicWorkstationListKey)
        vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
          try {
            var ret = JSON.parse(SM.decrypt(res, privateKey))
            console.log('获取所有站点列表', ret)
            var { total } = ret
            if (that.pageNo === 1) {
              that.dataList = ret.data || []
            } else {
              that.dataList = that.dataList.concat(ret.data)
            }
            that.pageNo = that.pageNo + 1
            that.loading = false
            that.flag = true
            // 数据全部加载完成
            if (that.dataList.length >= total) {
              that.finished = true
            }
          } catch (error) {
            // 在这里处理异常情况
            console.log('解析JSON时发生错误：', error)
            that.getWorkstationList()
          }
        })
      },
      // 手机号脱敏
      maskMobile (mobile) {
        return mobile.slice(0, 3) + "*".repeat(6) + mobile.slice(-2);
      },
      // 进详情
      openDetails (_item) {
        window.location.href = './workstationDetails.html?id=' + _item.id
      },
      // 跳转到地区选择页面
      goToCitySelect () {
        window.location.href = './chooseCity.html';
      },
    }
  })

  window.onscroll = function () {
    var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    app.scrollTop = scrollTop
  }
</script>

</body>

</html>