<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>站点活动详情</title>
  <link rel="stylesheet" href="../css/vant.css">
  <link rel="stylesheet" href="../css/common.css">
  <style>
    [v-cloak] {
      display: none;
    }

    .body_box {
      background: #fff;
      padding: 20px;
    }

    .activity_title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 12px;
      line-height: 1.4;
    }

    .status_tags {
      margin-bottom: 16px;
      gap: 10px;
    }

    .status_tag {
      padding: 4px 10px;
      border-radius: 4px;
      font-size: 12px;
    }

    .status_normal {
      background-color: #f0f0f0;
      color: #666;
    }

    .status_type {
      background-color: #e8f4ff;
      color: #3894FF;
    }

    .info_item {
      margin-bottom: 10px;
      line-height: 1.5;
    }

    .info_item:last-child {
      margin-bottom: 0;
    }

    .info_label {
      flex-shrink: 0;
      width: 80px;
      font-size: 15px;
      color: #666;
    }

    .info_value {
      font-size: 15px;
      color: #333;
    }

    .activity_content {
      padding: 12px 0;
    }

    .content_text {
      font-size: 15px;
      line-height: 1.6;
      color: #333;
    }

    /* .content_text p {
      margin-bottom: 10px;
    } */

    .content_text img {
      max-width: 100%;
      height: auto;
      margin: 10px 0;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app" v-cloak>
    <div class="activity_title">{{info.title}}</div>
    <!-- 状态标签 -->
    <div class="status_tags flex_box">
      <div class="status_tag status_normal">{{info.activityStatus}}</div>
      <div class="status_tag status_type" v-if="info.stationActivityType && info.stationActivityType.value">
        {{info.stationActivityType.name}}</div>
    </div>
    <!-- 活动信息列表 -->
    <div class="info_item flex_box">
      <span class="info_label">活动站点：</span>
      <span class="info_value flex_1">{{info.stationName}}</span>
    </div>
    <div class="info_item flex_box">
      <span class="info_label">活动时间：</span>
      <span class="info_value flex_1">{{info.beginTime}}至{{info.endTime}}</span>
    </div>
    <div class="info_item flex_box">
      <span class="info_label">活动地点：</span>
      <span class="info_value flex_1">{{info.address}}</span>
    </div>
    <!-- 活动内容 -->
    <div class="activity_content">
      <div class="content_text" v-html="info.content"></div>
    </div>
  </div>

  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    let id = ''
    var app = new Vue({
      el: "#app",
      data: {
        info: {
          title: '测试活动主题',
          activityStatus: '已结束',
          stationActivityType: {
            value: '1',
            name: '考察活动'
          },
          stationName: '阳谷县狮子楼街道顺发社区人大代表工作室',
          beginTime: '2025-10-30 00:00',
          endTime: '2025-10-31 00:00',
          address: '测试',
          content: '<p>测试活动内容详情</p><p>这里是活动的具体描述信息，可以包含多行文本内容。</p>'
        }
      },
      mounted () {
        const urlParams = new URLSearchParams(window.location.search);
        id = urlParams.get('id')
        if (id) {
          this.loadActivityDetails()
        }
      },
      methods: {
        loadActivityDetails () {
        },
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>