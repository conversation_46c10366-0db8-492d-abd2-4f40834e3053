<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>工作站详情</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      background: #f5f6fa;
      padding-bottom: 20px;
    }

    /* 顶部背景区域 */
    .workstation_details_top {
      position: relative;
      z-index: 1;
    }

    .workstation_details_top_img {
      width: 100%;
    }

    .header_title {
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      margin-bottom: 10px;
      text-align: center;
      margin-top: 30px;
    }

    .area_tags {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
    }

    .area_tag {
      background: rgba(255, 255, 255, 0.3);
      color: #fff;
      padding: 2px 12px;
      border-radius: 10px;
      font-size: 14px;
      margin: 0 5px;
    }

    /* 工作站信息卡片 */
    .workstation_card {
      background: #fff;
      margin: -30px 16px 16px;
      border-radius: 12px;
      padding: 5px 16px 16px 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      z-index: 2;
      position: relative;
    }

    .workstation_card_tag {
      background: #FAF2E0;
      border-radius: 4px;
      padding: 4px 16px;
      margin-top: 10px;
      margin-right: 10px;
    }

    .icon_model {
      width: 35px;
      height: 40px;
      margin-right: 10px;
    }

    .workstation_card_tag_text {
      font-size: 14px;
      color: #8B5413;
    }

    .station_name {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      margin-top: 10px;
      text-align: center;
    }

    .info_row {
      margin-bottom: 10px;
    }

    .info_icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .info_label {
      font-size: 14px;
      color: #666;
      width: 60px;
    }

    .info_value {
      font-size: 14px;
      color: #333;
      flex: 1;
    }

    /* 留言按钮 */
    .message_button {
      background: linear-gradient(90deg, #0D75FF 0%, #41BBFF 100%);
      color: #fff;
      border: none;
      border-radius: 20px;
      width: 100%;
      height: 40px;
      font-size: 16px;
      margin-top: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }

    .message_icon {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }

    /* 值班信息区域 */
    .duty_section {
      background: #fff;
      margin: 0 16px 16px;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .no_duty {
      text-align: center;
      color: #999;
      padding: 20px 0;
      font-size: 14px;
    }

    /* 值班项目样式 */
    .duty_item {
      margin-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .duty_item:last-child {
      margin-bottom: 0;
      border-bottom: none;
    }

    .duty_header {
      margin-bottom: 8px;
    }

    .duty_status {
      padding: 4px 10px;
      border-radius: 20px;
      font-size: 13px;
      background: #E5F0FF;
      color: #0D6AD8;
    }

    .duty_time {
      font-size: 14px;
      color: #666;
    }

    .duty_theme {
      font-size: 17px;
      font-weight: 500;
      color: #333;
      margin-bottom: 10px;
    }

    .duty_members {
      background-color: #F0F4FF;
      border-radius: 6px;
      padding: 12px 16px;
      margin-bottom: 10px;
    }

    .member_avatar {
      width: 46px;
      height: 45px;
      background: #D9D9D9;
      border-radius: 50%;
    }

    .member_info {
      margin-left: 20px;
      flex: 1;
    }

    .member_name_tag {
      margin-bottom: 6px;
    }

    .member_name {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      margin-right: 15px;
    }

    .member_tag {
      font-size: 12px;
      color: #0D6AD8;
      background: #E5F0FF;
      border-radius: 5px;
      padding: 4px 8px;
    }

    .member_position {
      font-weight: 500;
      font-size: 12px;
      color: #999999;
    }

    /* 驻站代表区域 */
    .representative_section {
      background: #fff;
      margin: 0 16px 16px;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .representative_list {
      overflow-y: hidden;
    }

    .representative_item {
      height: 140px;
      margin-right: 10px;
      border: 1px #eff2f5 solid;
      text-align: center;
    }

    .representative_item:last-child {
      border-bottom: none;
    }

    .avatar {
      width: 74px;
      height: 84px;
    }

    .rep_name {
      margin-top: 5px;
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .rep_position {
      margin: 2px 0;
      font-size: 12px;
      font-weight: 500;
      color: #999999;
    }

    /* 一站一品 */
    .one_station_one_product {
      background: #fff;
      margin: 0 16px 16px;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .one_product_title_area {
      padding-right: 20px;
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-right: 15px;
      line-height: 1.4;
      border-right: 1px solid #EEEEEE;
    }

    .one_station {
      color: #000;
    }

    .one_product {
      color: #FF3B30;
    }

    .one_product_content_title {
      font-size: 18px;
      font-weight: 800;
      color: #000;
      margin-bottom: 8px;
    }

    .one_product_content_text {
      font-size: 16px;
      color: #666;
      line-height: 1.5;
    }

    /* tab信息 */
    .tab_content_card {
      background: #fff;
      margin: 0 16px 16px;
      border-radius: 12px;
      padding: 0 16px 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .introduce {
      font-size: 15px;
      padding: 16px 0;
      font-family: PingFang SC-Medium;
      line-height: 24px;
      color: #333333;
      text-indent: 2em;
    }

    .van-search {
      padding: 0;
    }

    .search_box {
      padding: 10px 0;
    }

    /* 民情民意相关 */
    .public_opinion_item {
      padding: 14px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .opinion_title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      line-height: 22px;
      margin-right: 10px;
    }

    .status_pending {
      padding: 2px 10px;
      border-radius: 5px;
      font-size: 12px;
      white-space: nowrap;
      color: #FF6B35;
      background-color: #FFF2ED;
    }

    .status_evaluated {
      padding: 2px 10px;
      border-radius: 5px;
      font-size: 12px;
      white-space: nowrap;
      color: #36B37E;
      background-color: #E6F7F0;
    }

    .status_reply {
      padding: 2px 10px;
      border-radius: 5px;
      font-size: 12px;
      white-space: nowrap;
      color: rgb(56, 148, 255);
      background-color: rgba(56, 148, 255, 0.1);
    }

    .opinion_author {
      font-size: 14px;
      color: #666;
    }

    .opinion_date {
      font-size: 14px;
      color: #999;
    }

    /* Van-Tabs左右滑动样式 */
    .custom-scrollable-tabs .van-tabs__nav-wrap {
      overflow-x: auto !important;
      overflow-y: hidden !important;
    }

    .custom-scrollable-tabs .van-tabs__nav {
      overflow-x: auto !important;
      overflow-y: hidden !important;
      width: auto !important;
      min-width: 100% !important;
      flex-wrap: nowrap !important;
      white-space: nowrap !important;
    }

    .van-tab {
      padding: 0 10px;
    }

    .custom-scrollable-tabs .van-tabs__nav::-webkit-scrollbar {
      display: none;
    }

    .custom-scrollable-tabs .van-tabs__nav--line {
      padding-bottom: 8px;
    }

    .custom-scrollable-tabs .van-tabs__line {
      bottom: 8px !important;
    }

    /* 隐藏vant自带的左右箭头 */
    .custom-scrollable-tabs .van-tabs__nav__arrow {
      display: none !important;
    }

    /* 信息发布相关 */
    .info_tab_container {
      overflow-x: auto;
      white-space: nowrap;
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .info_tab_container::-webkit-scrollbar {
      display: none;
    }

    .info_tab_item {
      padding: 6px 16px;
      margin-right: 10px;
      font-size: 14px;
      color: #666;
      border-radius: 16px;
      background-color: #f5f5f5;
      flex-shrink: 0;
    }

    .info_tab_item.active {
      color: #3894FF;
      background-color: #E6F2FF;
      font-weight: 500;
    }

    .info_item {
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .info_title {
      font-size: 17px;
      font-weight: 500;
      color: #333;
      line-height: 22px;
      margin-bottom: 10px;
    }

    .info_date {
      font-size: 14px;
      color: #999;
    }

    /* 站点活动相关 */
    .activity_item {
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .activity_title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      line-height: 22px;
      margin-bottom: 10px;
    }

    .activity_time {
      font-size: 14px;
      color: #666;
    }

    .activity_status {
      padding: 2px 10px;
      font-size: 12px;
      color: #666;
      background-color: #f5f5f5;
      border-radius: 6px;
      white-space: nowrap;
    }

    .nodata {
      margin: 30px 0;
      text-align: center;
      color: #ccc;
      width: 100%;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 顶部背景区域 -->
    <div class="workstation_details_top">
      <img :src="info.showImgs?fileImgUrl+info.showImgs:'../img/workstation_default.png'" mode=""
        class="workstation_details_top_img" />
    </div>

    <!-- 工作站信息卡片 -->
    <div class="workstation_card">
      <div class="flex_box flex_justify_between">
        <div class="flex_wrap flex_box flex_align_center flex_1">
          <div class="flex_box workstation_card_tag" v-cloak v-if="info.excellentStationType.value">
            <img src="../img/icon_model.png" mode="" class="icon_model" />
            <span class="workstation_card_tag_text">{{info.excellentStationType.name}}</span>
          </div>
          <div class="workstation_card_tag" v-if="info.cityName" v-cloak>
            <span class="workstation_card_tag_text">{{info.cityName}}</span>
          </div>
          <div class="workstation_card_tag" v-if="info.countryName" v-cloak>
            <span class="workstation_card_tag_text">{{info.countryName}}</span>
          </div>
        </div>
      </div>

      <h2 class="station_name two_text" v-cloak>{{info.name || '阳谷县狮子楼街道顺发社区人大代表工作室'}}</h2>

      <div class="info_row flex_box flex_align_center">
        <img src="../img/icon_user.png" alt="联系人" class="info_icon" />
        <span class="info_label">联系人</span>
        <span class="info_value" v-cloak>{{info.contactUserName || '张武'}}</span>
      </div>

      <div class="info_row flex_box flex_align_center">
        <img src="../img/icon_phone.png" alt="电话" class="info_icon" />
        <span class="info_label">电话</span>
        <span class="info_value" v-cloak>{{info.contactTelephone || '15566666666'}}</span>
      </div>

      <div class="info_row flex_box flex_align_center">
        <img src="../img/icon_time.png" alt="接待时间" class="info_icon" />
        <span class="info_label">接待时间</span>
        <span class="info_value" v-cloak>{{info.openTime || '8:00-18:00'}}</span>
      </div>

      <div class="info_row flex_box flex_align_center">
        <img src="../img/icon_location.png" alt="站点地址" class="info_icon" />
        <span class="info_label">站点地址</span>
        <span class="info_value two_text" v-cloak>{{info.address || '阳谷县狮子楼街道顺发社区人大代表工作室'}}</span>
      </div>

      <button class="message_button" @click="goToMessage">
        <img src="../img/icon_msg.png" alt="留言" class="message_icon" />
        我要留言
      </button>
    </div>

    <!-- 今日值班信息 -->
    <div class="duty_section">
      <div class="section-header flex_box flex_align_center">
        <div class="section-line"></div>
        <span class="section-title">今日代表值班</span>
      </div>
      <!-- 有值班信息时显示 -->
      <div v-if="dutyList && dutyList.length > 0" v-cloak>
        <div class="duty_item" v-for="(duty, index) in dutyList" :key="index">
          <div class="duty_header flex_box flex_justify_between flex_align_center">
            <div class="duty_time">{{ formatTime(duty.startTime) }} - {{ formatTime(duty.endTime) }}</div>
            <div class="duty_status">{{ duty.status }}</div>
          </div>
          <div class="duty_theme">{{ duty.theme }}</div>
          <div class="duty_members flex_box flex_align_center" v-for="(member, mIndex) in duty.memberList" :key="mIndex"
            @click="goToLeaveMessageToRepresentative(member)">
            <img :src="member.photo ? fileImgUrl + member.photo : '../img/def_head_img.jpg'" alt="头像"
              class="member_avatar" />
            <div class="member_info">
              <div class="member_name_tag flex_box flex_align_center">
                <div class="member_name">{{ member.userName }}</div>
                <div class="member_tag">
                  {{member.topUserRole=='nation'?'国':member.topUserRole=='province'?'省':member.topUserRole=='city'?'市':member.topUserRole=='county'?'区/县':member.topUserRole=='town'?'乡镇(街道)':''}}人大代表
                </div>
              </div>
              <div class="member_position">{{member.position}}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 无值班信息时显示 -->
      <div v-else class="no_duty">今日暂无代表值班</div>
    </div>

    <!-- 驻站代表列表 -->
    <div class="representative_section">
      <div class="section-header flex_box flex_align_center">
        <div class="section-line"></div>
        <span class="section-title" v-cloak>驻站代表（{{representatives.length || 1}}）</span>
      </div>
      <div class="representative_list flex_box">
        <div class="representative_item" v-for="(item, index) in representatives" :key="index"
          @click="goToLeaveMessageToRepresentative(item)">
          <img class="avatar"
            :src="item.headImg?fileImgUrl+item.headImg:item.photo?fileImgUrl+item.photo:'../img/def_head_img.jpg'"
            alt="头像" />
          <div class="rep_name two_text" v-cloak>{{item.userName}}</div>
          <div class="rep_position two_text" v-cloak>
            {{item.topUserRole=='nation'?'国':item.topUserRole=='province'?'省':item.topUserRole=='city'?'市':item.topUserRole=='county'?'区县':item.topUserRole=='town'?'乡镇(街道)':''}}人大代表
          </div>
        </div>
      </div>
    </div>

    <!-- 一站一品 -->
    <div class="one_station_one_product" v-if="featuredProduct.id">
      <div class="flex_box flex_align_center">
        <div class="one_product_title_area">
          <div class="one_station">一站</div>
          <div class="one_product">一品</div>
        </div>
        <div class="flex_1" @click="openFeaturedMore">
          <div class="one_product_content_title one_text" v-cloak>{{featuredProduct.title || '标题标题'}}</div>
          <div class="one_product_content_text one_text" v-cloak>{{featuredProduct.name || '测试'}}</div>
        </div>
      </div>
    </div>

    <!-- 信息 -->
    <div class="tab_content_card">
      <van-tabs v-model="active" @click="onClickTab" color="#3894FF" title-active-color="#333333"
        title-inactive-color="#999999" class="custom-scrollable-tabs">
        <van-tab title="简介">
          <div class="introduce" v-cloak>{{info.introduction || '暂无'}}</div>
        </van-tab>
        <van-tab title="民情民意" v-cloak>
          <div class="search_box">
            <van-search v-model="searchKeyword" placeholder="请输入关键词" />
          </div>
          <template v-if="publicOpinionData&&publicOpinionData.length>0">
            <div class="public_opinion_list" v-for="item in publicOpinionData" @click="openStationLetterDetails(item)">
              <div class="public_opinion_item">
                <div class="flex_box flex_align_center" style="margin-bottom: 10px;">
                  <div class="opinion_title flex_1">{{item.title}}</div>
                  <div
                    :class="item.letterStatus==='未回复'?'status_pending':item.letterStatus==='已评价'?'status_evaluated':item.letterStatus==='已回复'?'status_reply':''">
                    {{item.letterStatus}}
                  </div>
                </div>
                <div class="flex_box flex_align_center flex_justify_between">
                  <div class="opinion_author">{{item.senderUserName.charAt(0) + '*'.repeat(item.senderUserName.length -
                    1)}}</div>
                  <div class="opinion_date">{{formatTime(item.receiveTime)}}</div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </van-tab>
        <van-tab title="信息发布" v-cloak>
          <div class="info_tab_container flex_box" v-if="infomationCategoryColumn.length>0">
            <div :class="['info_tab_item', {active: index === 0}]" @click="selectTab(item, index)"
              v-for="(item, index) in infomationCategoryColumn" :key="item.id">{{item.name}}</div>
          </div>
          <div class="search_box">
            <van-search v-model="searchKeyword" placeholder="请输入关键词" />
          </div>
          <template v-if="infoList&&infoList.length!==0">
            <div class="info_list" v-for="item in infoList" @click="openInformationReleaseDetails(item)">
              <div class="info_item">
                <div class="info_title two_text">{{item.infoTitle}}</div>
                <div class="info_date">{{formatTime(item.pubTime)}}</div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </van-tab>
        <van-tab title="站点活动" v-cloak>
          <div class="info_tab_container flex_box" v-if="activityCategoryColumn.length>0">
            <div :class="['info_tab_item', {active: index === 0}]" @click="selectTab(item, index)"
              v-for="(item, index) in activityCategoryColumn" :key="item.id">{{item.name}}</div>
          </div>
          <div class="search_box">
            <van-search v-model="searchKeyword" placeholder="请输入关键词" />
          </div>
          <template v-if="activityList&&activityList.length!==0">
            <div class="activity_list" v-for="item in activityList" @click="activityItemClick(item)">
              <div class="activity_item">
                <div class="activity_title">{{item.title}}</div>
                <div class="flex_box flex_align_center flex_justify_between">
                  <div class="activity_time flex_1">{{item.beginTime}}至{{item.endTime}}</div>
                  <div class="activity_status">{{item.activityStatus}}</div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </van-tab>
        <van-tab title="国家机关进站" v-cloak>
          <div class="info_tab_container flex_box" v-if="stateStationCategoryColumn.length>0">
            <div :class="['info_tab_item', {active: index === 0}]" @click="selectTab(item, index)"
              v-for="(item, index) in stateStationCategoryColumn" :key="item.id">{{item.name}}</div>
          </div>
          <div class="search_box">
            <van-search v-model="searchKeyword" placeholder="请输入关键词" />
          </div>
          <template v-if="stateStationList&&stateStationList.length!==0">
            <div class="info_list" v-for="item in stateStationList" @click="stateStationItemClick(item)">
              <div class="info_item">
                <div class="info_title two_text">{{item.infoTitle}}</div>
                <div class="info_date">{{formatTime(item.pubTime)}}</div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </van-tab>
      </van-tabs>
    </div>
  </div>
  </div>

  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>

  <script>
    var allListKey = '4e2524722f0b7de6bc9a49b4e955a16809c0a8630d8b3b38388bc15e2c67bdc8' // 获取工作室详情私钥
    var publicAllListKey = '04e68f60ab0efc9c89cb966d6dd43f1bd6de9a47ee19395c84a00c87cfc8af135466b4b97fdb373f23db9c72d90a14f3cfc2d6aaf8f67e0dffab36dad384cf2319' 	// 获取工作室详情公钥
    var id = ''
    var app = new Vue({
      el: "#app",
      data: {
        fileImgUrl: 'http://**************:8084/lzt/image/',
        info: { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "id": "1874029185060597762", "name": "阳谷县狮子楼街道顺发社区人大代表工作室", "memberAmount": 1, "longitude": "115.795721", "latitude": "36.099892", "masterTypeCode": "", "isMasterStation": 0, "managerAreaId": null, "province": "370000", "city": "371500", "country": "371521", "town": "", "address": "阳谷县狮子楼街道顺发社区人大代表工作室", "showImgs": "", "contactUserName": "张武", "contactTelephone": "15566666666", "secondContactUserName": null, "secondContactTel": null, "contactDeputyName": null, "contactDeputyTel": null, "entityCreateDate": null, "openTime": "8：00-18：00", "isExcellentStation": 0, "excellentStationType": { "value": null, "label": null, "name": null, "dictCode": null }, "baseStationTag": { "value": null, "label": null, "name": null, "dictCode": null }, "videoIntro": null, "introduction": "狮子楼街道工作室=================================", "sort": 157, "talkGroupId": null, "isUsing": 1, "createDate": 1735638350155, "createBy": "1", "workerIds": [], "workers": [], "adminIds": null, "admins": [], "masterTypeName": "", "provinceName": "山东省", "cityName": "聊城市", "countryName": "阳谷县", "townName": "", "managerAreaName": "", "hasFocus": null },
        dutyList: [
          {
            "id": "198373556**********",
            "theme": "测试值班主题",
            "startTime": *************,
            "endTime": *************,
            "stationId": "1874029185060597762",
            "createBy": "Admin",
            "createDate": *************,
            "stationName": "阳谷县狮子楼街道顺发社区人大代表工作室",
            "status": "进行中",
            "members": "",
            "memberList": [
              {
                "id": "1983098396365316098",
                "accountId": "1983098396176572417",
                "sort": null,
                "userName": "测试-阳谷1",
                "headImg": null,
                "topUserRole": "county",
                "photo": "43a7f8d8-ea9a-4994-9041-2c77886de37e.png",
                "mobile": "************",
                "position": ""
              }
            ]
          },
          {
            "id": "1983736314004008962",
            "theme": "值班主题",
            "startTime": *************,
            "endTime": *************,
            "stationId": "1874029185060597762",
            "createBy": "Admin",
            "createDate": *************,
            "stationName": "阳谷县狮子楼街道顺发社区人大代表工作室",
            "status": "进行中",
            "members": "",
            "memberList": [
              {
                "id": "1983736168570712065",
                "accountId": "1983736168520380417",
                "sort": null,
                "userName": "测试11",
                "headImg": "",
                "topUserRole": "city",
                "photo": "********-0059-40a0-8f9d-a21369c22e11.jpg",
                "mobile": "***********",
                "position": "技术"
              },
              {
                "id": "1983098396365316098",
                "accountId": "1983098396176572417",
                "sort": null,
                "userName": "测试-阳谷1",
                "headImg": null,
                "topUserRole": "county",
                "photo": "43a7f8d8-ea9a-4994-9041-2c77886de37e.png",
                "mobile": "************",
                "position": ""
              }
            ]
          }
        ],
        representatives: [
          { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "userName": "测试11", "oldName": "", "oldNameSign": null, "id": "1983736168570712065", "areaId": "371500", "authAreaScope": "371500", "accountId": "1983736168520380417", "isJoinApp": 0, "joinAppDate": null, "isUsing": 1, "isReceiveMsg": 1, "isPartyMember": 0, "headImg": "", "photo": "********-0059-40a0-8f9d-a21369c22e11.jpg", "account": "cs1122333226", "mobile": "***********", "isShowMobile": 1, "sex": { "value": "1", "label": "男", "name": "男", "dictCode": "sex" }, "birthPlace": "", "nation": { "value": null, "label": null, "name": null, "dictCode": null }, "officeId": "", "position": "技术", "postcode": "", "callAddress": "", "territory": "", "email": "", "health": { "value": null, "label": null, "name": null, "dictCode": null }, "party": { "value": null, "label": null, "name": null, "dictCode": null }, "nativePlace": "", "honorInfo": null, "contribute": null, "officePhone": "", "school": "", "specialty": { "value": null, "label": null, "name": null, "dictCode": null }, "degree": { "value": null, "label": null, "name": null, "dictCode": null }, "education": { "value": null, "label": null, "name": null, "dictCode": null }, "mobileRemarks": "", "introduction": null, "professional": { "value": null, "label": null, "name": null, "dictCode": null }, "politicsLevel": { "value": null, "label": null, "name": null, "dictCode": null }, "committee": { "value": null, "label": null, "name": null, "dictCode": null }, "homeAddress": "", "partyMemberType": { "value": null, "label": null, "name": null, "dictCode": null }, "carNumber": "", "idCard": "", "birthday": null, "joinWorkDate": null, "joinPartyDate": null, "sort": 0, "sort11": 9, "sort12": "441253422", "createBy": "1", "createDate": 1761794533281, "remarks": "11", "birthdayMonth": null, "birthdayDay": null, "birthdayAge": null, "userNamePinyin": "ceshi11", "userNamePinyinFirst": "cs11", "representerElement": { "value": null, "label": null, "name": null, "dictCode": null }, "commentCodeType": 1, "nationId": "", "provinceId": "", "cityId": "371500", "countyId": "", "townId": "", "isMultiAreaRole": 0, "isMultiInStation": 0, "inStationCount": 1, "stationMemberFileId": null, "isEnter": 0, "isStationMember": 89118, "stationSort": 1, "stationNames": "阳谷县狮子楼街道顺发社区人大代表工作室", "stationIds": null, "areaIds": null, "areaName": "聊城市人大代表", "topUserRole": "city", "hasInStation": 1 },
          { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "userName": "测试-阳谷1", "oldName": "", "oldNameSign": null, "id": "1983098396365316098", "areaId": "371500", "authAreaScope": "371500", "accountId": "1983098396176572417", "isJoinApp": 0, "joinAppDate": null, "isUsing": 1, "isReceiveMsg": 1, "isPartyMember": 0, "headImg": null, "photo": "43a7f8d8-ea9a-4994-9041-2c77886de37e.png", "account": "cs-yg129533567", "mobile": "************", "isShowMobile": 1, "sex": { "value": "1", "label": "男", "name": "男", "dictCode": "sex" }, "birthPlace": "", "nation": { "value": null, "label": null, "name": null, "dictCode": null }, "officeId": "", "position": "", "postcode": "", "callAddress": "", "territory": "", "email": "", "health": { "value": null, "label": null, "name": null, "dictCode": null }, "party": { "value": null, "label": null, "name": null, "dictCode": null }, "nativePlace": "", "honorInfo": null, "contribute": null, "officePhone": "", "school": "", "specialty": { "value": null, "label": null, "name": null, "dictCode": null }, "degree": { "value": null, "label": null, "name": null, "dictCode": null }, "education": { "value": null, "label": null, "name": null, "dictCode": null }, "mobileRemarks": "", "introduction": null, "professional": { "value": null, "label": null, "name": null, "dictCode": null }, "politicsLevel": { "value": null, "label": null, "name": null, "dictCode": null }, "committee": { "value": null, "label": null, "name": null, "dictCode": null }, "homeAddress": "", "partyMemberType": { "value": null, "label": null, "name": null, "dictCode": null }, "carNumber": "", "idCard": "", "birthday": null, "joinWorkDate": null, "joinPartyDate": null, "sort": 0, "sort11": 9, "sort12": "441253422", "createBy": "1", "createDate": 1761642476537, "remarks": "", "birthdayMonth": null, "birthdayDay": null, "birthdayAge": null, "userNamePinyin": "ceshi-yanggu1", "userNamePinyinFirst": "cs-yg1", "representerElement": { "value": null, "label": null, "name": null, "dictCode": null }, "commentCodeType": 1, "nationId": "", "provinceId": "", "cityId": "", "countyId": "371521", "townId": "", "isMultiAreaRole": 0, "isMultiInStation": 0, "inStationCount": 1, "stationMemberFileId": null, "isEnter": 0, "isStationMember": 6968, "stationSort": 0, "stationNames": "阳谷县狮子楼街道顺发社区人大代表工作室", "stationIds": null, "areaIds": null, "areaName": "阳谷县人大代表", "topUserRole": "county", "hasInStation": 1 },
          { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "userName": "测试11", "oldName": "", "oldNameSign": null, "id": "1983736168570712065", "areaId": "371500", "authAreaScope": "371500", "accountId": "1983736168520380417", "isJoinApp": 0, "joinAppDate": null, "isUsing": 1, "isReceiveMsg": 1, "isPartyMember": 0, "headImg": "", "photo": "********-0059-40a0-8f9d-a21369c22e11.jpg", "account": "cs1122333226", "mobile": "***********", "isShowMobile": 1, "sex": { "value": "1", "label": "男", "name": "男", "dictCode": "sex" }, "birthPlace": "", "nation": { "value": null, "label": null, "name": null, "dictCode": null }, "officeId": "", "position": "技术", "postcode": "", "callAddress": "", "territory": "", "email": "", "health": { "value": null, "label": null, "name": null, "dictCode": null }, "party": { "value": null, "label": null, "name": null, "dictCode": null }, "nativePlace": "", "honorInfo": null, "contribute": null, "officePhone": "", "school": "", "specialty": { "value": null, "label": null, "name": null, "dictCode": null }, "degree": { "value": null, "label": null, "name": null, "dictCode": null }, "education": { "value": null, "label": null, "name": null, "dictCode": null }, "mobileRemarks": "", "introduction": null, "professional": { "value": null, "label": null, "name": null, "dictCode": null }, "politicsLevel": { "value": null, "label": null, "name": null, "dictCode": null }, "committee": { "value": null, "label": null, "name": null, "dictCode": null }, "homeAddress": "", "partyMemberType": { "value": null, "label": null, "name": null, "dictCode": null }, "carNumber": "", "idCard": "", "birthday": null, "joinWorkDate": null, "joinPartyDate": null, "sort": 0, "sort11": 9, "sort12": "441253422", "createBy": "1", "createDate": 1761794533281, "remarks": "11", "birthdayMonth": null, "birthdayDay": null, "birthdayAge": null, "userNamePinyin": "ceshi11", "userNamePinyinFirst": "cs11", "representerElement": { "value": null, "label": null, "name": null, "dictCode": null }, "commentCodeType": 1, "nationId": "", "provinceId": "", "cityId": "371500", "countyId": "", "townId": "", "isMultiAreaRole": 0, "isMultiInStation": 0, "inStationCount": 1, "stationMemberFileId": null, "isEnter": 0, "isStationMember": 89118, "stationSort": 1, "stationNames": "阳谷县狮子楼街道顺发社区人大代表工作室", "stationIds": null, "areaIds": null, "areaName": "聊城市人大代表", "topUserRole": "city", "hasInStation": 1 },
          { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "userName": "测试-阳谷1", "oldName": "", "oldNameSign": null, "id": "1983098396365316098", "areaId": "371500", "authAreaScope": "371500", "accountId": "1983098396176572417", "isJoinApp": 0, "joinAppDate": null, "isUsing": 1, "isReceiveMsg": 1, "isPartyMember": 0, "headImg": null, "photo": "43a7f8d8-ea9a-4994-9041-2c77886de37e.png", "account": "cs-yg129533567", "mobile": "************", "isShowMobile": 1, "sex": { "value": "1", "label": "男", "name": "男", "dictCode": "sex" }, "birthPlace": "", "nation": { "value": null, "label": null, "name": null, "dictCode": null }, "officeId": "", "position": "", "postcode": "", "callAddress": "", "territory": "", "email": "", "health": { "value": null, "label": null, "name": null, "dictCode": null }, "party": { "value": null, "label": null, "name": null, "dictCode": null }, "nativePlace": "", "honorInfo": null, "contribute": null, "officePhone": "", "school": "", "specialty": { "value": null, "label": null, "name": null, "dictCode": null }, "degree": { "value": null, "label": null, "name": null, "dictCode": null }, "education": { "value": null, "label": null, "name": null, "dictCode": null }, "mobileRemarks": "", "introduction": null, "professional": { "value": null, "label": null, "name": null, "dictCode": null }, "politicsLevel": { "value": null, "label": null, "name": null, "dictCode": null }, "committee": { "value": null, "label": null, "name": null, "dictCode": null }, "homeAddress": "", "partyMemberType": { "value": null, "label": null, "name": null, "dictCode": null }, "carNumber": "", "idCard": "", "birthday": null, "joinWorkDate": null, "joinPartyDate": null, "sort": 0, "sort11": 9, "sort12": "441253422", "createBy": "1", "createDate": 1761642476537, "remarks": "", "birthdayMonth": null, "birthdayDay": null, "birthdayAge": null, "userNamePinyin": "ceshi-yanggu1", "userNamePinyinFirst": "cs-yg1", "representerElement": { "value": null, "label": null, "name": null, "dictCode": null }, "commentCodeType": 1, "nationId": "", "provinceId": "", "cityId": "", "countyId": "371521", "townId": "", "isMultiAreaRole": 0, "isMultiInStation": 0, "inStationCount": 1, "stationMemberFileId": null, "isEnter": 0, "isStationMember": 6968, "stationSort": 0, "stationNames": "阳谷县狮子楼街道顺发社区人大代表工作室", "stationIds": null, "areaIds": null, "areaName": "阳谷县人大代表", "topUserRole": "county", "hasInStation": 1 },
          { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "userName": "测试11", "oldName": "", "oldNameSign": null, "id": "1983736168570712065", "areaId": "371500", "authAreaScope": "371500", "accountId": "1983736168520380417", "isJoinApp": 0, "joinAppDate": null, "isUsing": 1, "isReceiveMsg": 1, "isPartyMember": 0, "headImg": "", "photo": "********-0059-40a0-8f9d-a21369c22e11.jpg", "account": "cs1122333226", "mobile": "***********", "isShowMobile": 1, "sex": { "value": "1", "label": "男", "name": "男", "dictCode": "sex" }, "birthPlace": "", "nation": { "value": null, "label": null, "name": null, "dictCode": null }, "officeId": "", "position": "技术", "postcode": "", "callAddress": "", "territory": "", "email": "", "health": { "value": null, "label": null, "name": null, "dictCode": null }, "party": { "value": null, "label": null, "name": null, "dictCode": null }, "nativePlace": "", "honorInfo": null, "contribute": null, "officePhone": "", "school": "", "specialty": { "value": null, "label": null, "name": null, "dictCode": null }, "degree": { "value": null, "label": null, "name": null, "dictCode": null }, "education": { "value": null, "label": null, "name": null, "dictCode": null }, "mobileRemarks": "", "introduction": null, "professional": { "value": null, "label": null, "name": null, "dictCode": null }, "politicsLevel": { "value": null, "label": null, "name": null, "dictCode": null }, "committee": { "value": null, "label": null, "name": null, "dictCode": null }, "homeAddress": "", "partyMemberType": { "value": null, "label": null, "name": null, "dictCode": null }, "carNumber": "", "idCard": "", "birthday": null, "joinWorkDate": null, "joinPartyDate": null, "sort": 0, "sort11": 9, "sort12": "441253422", "createBy": "1", "createDate": 1761794533281, "remarks": "11", "birthdayMonth": null, "birthdayDay": null, "birthdayAge": null, "userNamePinyin": "ceshi11", "userNamePinyinFirst": "cs11", "representerElement": { "value": null, "label": null, "name": null, "dictCode": null }, "commentCodeType": 1, "nationId": "", "provinceId": "", "cityId": "371500", "countyId": "", "townId": "", "isMultiAreaRole": 0, "isMultiInStation": 0, "inStationCount": 1, "stationMemberFileId": null, "isEnter": 0, "isStationMember": 89118, "stationSort": 1, "stationNames": "阳谷县狮子楼街道顺发社区人大代表工作室", "stationIds": null, "areaIds": null, "areaName": "聊城市人大代表", "topUserRole": "city", "hasInStation": 1 },
          { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "userName": "测试-阳谷1", "oldName": "", "oldNameSign": null, "id": "1983098396365316098", "areaId": "371500", "authAreaScope": "371500", "accountId": "1983098396176572417", "isJoinApp": 0, "joinAppDate": null, "isUsing": 1, "isReceiveMsg": 1, "isPartyMember": 0, "headImg": null, "photo": "43a7f8d8-ea9a-4994-9041-2c77886de37e.png", "account": "cs-yg129533567", "mobile": "************", "isShowMobile": 1, "sex": { "value": "1", "label": "男", "name": "男", "dictCode": "sex" }, "birthPlace": "", "nation": { "value": null, "label": null, "name": null, "dictCode": null }, "officeId": "", "position": "", "postcode": "", "callAddress": "", "territory": "", "email": "", "health": { "value": null, "label": null, "name": null, "dictCode": null }, "party": { "value": null, "label": null, "name": null, "dictCode": null }, "nativePlace": "", "honorInfo": null, "contribute": null, "officePhone": "", "school": "", "specialty": { "value": null, "label": null, "name": null, "dictCode": null }, "degree": { "value": null, "label": null, "name": null, "dictCode": null }, "education": { "value": null, "label": null, "name": null, "dictCode": null }, "mobileRemarks": "", "introduction": null, "professional": { "value": null, "label": null, "name": null, "dictCode": null }, "politicsLevel": { "value": null, "label": null, "name": null, "dictCode": null }, "committee": { "value": null, "label": null, "name": null, "dictCode": null }, "homeAddress": "", "partyMemberType": { "value": null, "label": null, "name": null, "dictCode": null }, "carNumber": "", "idCard": "", "birthday": null, "joinWorkDate": null, "joinPartyDate": null, "sort": 0, "sort11": 9, "sort12": "441253422", "createBy": "1", "createDate": 1761642476537, "remarks": "", "birthdayMonth": null, "birthdayDay": null, "birthdayAge": null, "userNamePinyin": "ceshi-yanggu1", "userNamePinyinFirst": "cs-yg1", "representerElement": { "value": null, "label": null, "name": null, "dictCode": null }, "commentCodeType": 1, "nationId": "", "provinceId": "", "cityId": "", "countyId": "371521", "townId": "", "isMultiAreaRole": 0, "isMultiInStation": 0, "inStationCount": 1, "stationMemberFileId": null, "isEnter": 0, "isStationMember": 6968, "stationSort": 0, "stationNames": "阳谷县狮子楼街道顺发社区人大代表工作室", "stationIds": null, "areaIds": null, "areaName": "阳谷县人大代表", "topUserRole": "county", "hasInStation": 1 },
          { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "userName": "测试11", "oldName": "", "oldNameSign": null, "id": "1983736168570712065", "areaId": "371500", "authAreaScope": "371500", "accountId": "1983736168520380417", "isJoinApp": 0, "joinAppDate": null, "isUsing": 1, "isReceiveMsg": 1, "isPartyMember": 0, "headImg": "", "photo": "********-0059-40a0-8f9d-a21369c22e11.jpg", "account": "cs1122333226", "mobile": "***********", "isShowMobile": 1, "sex": { "value": "1", "label": "男", "name": "男", "dictCode": "sex" }, "birthPlace": "", "nation": { "value": null, "label": null, "name": null, "dictCode": null }, "officeId": "", "position": "技术", "postcode": "", "callAddress": "", "territory": "", "email": "", "health": { "value": null, "label": null, "name": null, "dictCode": null }, "party": { "value": null, "label": null, "name": null, "dictCode": null }, "nativePlace": "", "honorInfo": null, "contribute": null, "officePhone": "", "school": "", "specialty": { "value": null, "label": null, "name": null, "dictCode": null }, "degree": { "value": null, "label": null, "name": null, "dictCode": null }, "education": { "value": null, "label": null, "name": null, "dictCode": null }, "mobileRemarks": "", "introduction": null, "professional": { "value": null, "label": null, "name": null, "dictCode": null }, "politicsLevel": { "value": null, "label": null, "name": null, "dictCode": null }, "committee": { "value": null, "label": null, "name": null, "dictCode": null }, "homeAddress": "", "partyMemberType": { "value": null, "label": null, "name": null, "dictCode": null }, "carNumber": "", "idCard": "", "birthday": null, "joinWorkDate": null, "joinPartyDate": null, "sort": 0, "sort11": 9, "sort12": "441253422", "createBy": "1", "createDate": 1761794533281, "remarks": "11", "birthdayMonth": null, "birthdayDay": null, "birthdayAge": null, "userNamePinyin": "ceshi11", "userNamePinyinFirst": "cs11", "representerElement": { "value": null, "label": null, "name": null, "dictCode": null }, "commentCodeType": 1, "nationId": "", "provinceId": "", "cityId": "371500", "countyId": "", "townId": "", "isMultiAreaRole": 0, "isMultiInStation": 0, "inStationCount": 1, "stationMemberFileId": null, "isEnter": 0, "isStationMember": 89118, "stationSort": 1, "stationNames": "阳谷县狮子楼街道顺发社区人大代表工作室", "stationIds": null, "areaIds": null, "areaName": "聊城市人大代表", "topUserRole": "city", "hasInStation": 1 },
          { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "userName": "测试-阳谷1", "oldName": "", "oldNameSign": null, "id": "1983098396365316098", "areaId": "371500", "authAreaScope": "371500", "accountId": "1983098396176572417", "isJoinApp": 0, "joinAppDate": null, "isUsing": 1, "isReceiveMsg": 1, "isPartyMember": 0, "headImg": null, "photo": "43a7f8d8-ea9a-4994-9041-2c77886de37e.png", "account": "cs-yg129533567", "mobile": "************", "isShowMobile": 1, "sex": { "value": "1", "label": "男", "name": "男", "dictCode": "sex" }, "birthPlace": "", "nation": { "value": null, "label": null, "name": null, "dictCode": null }, "officeId": "", "position": "", "postcode": "", "callAddress": "", "territory": "", "email": "", "health": { "value": null, "label": null, "name": null, "dictCode": null }, "party": { "value": null, "label": null, "name": null, "dictCode": null }, "nativePlace": "", "honorInfo": null, "contribute": null, "officePhone": "", "school": "", "specialty": { "value": null, "label": null, "name": null, "dictCode": null }, "degree": { "value": null, "label": null, "name": null, "dictCode": null }, "education": { "value": null, "label": null, "name": null, "dictCode": null }, "mobileRemarks": "", "introduction": null, "professional": { "value": null, "label": null, "name": null, "dictCode": null }, "politicsLevel": { "value": null, "label": null, "name": null, "dictCode": null }, "committee": { "value": null, "label": null, "name": null, "dictCode": null }, "homeAddress": "", "partyMemberType": { "value": null, "label": null, "name": null, "dictCode": null }, "carNumber": "", "idCard": "", "birthday": null, "joinWorkDate": null, "joinPartyDate": null, "sort": 0, "sort11": 9, "sort12": "441253422", "createBy": "1", "createDate": 1761642476537, "remarks": "", "birthdayMonth": null, "birthdayDay": null, "birthdayAge": null, "userNamePinyin": "ceshi-yanggu1", "userNamePinyinFirst": "cs-yg1", "representerElement": { "value": null, "label": null, "name": null, "dictCode": null }, "commentCodeType": 1, "nationId": "", "provinceId": "", "cityId": "", "countyId": "371521", "townId": "", "isMultiAreaRole": 0, "isMultiInStation": 0, "inStationCount": 1, "stationMemberFileId": null, "isEnter": 0, "isStationMember": 6968, "stationSort": 0, "stationNames": "阳谷县狮子楼街道顺发社区人大代表工作室", "stationIds": null, "areaIds": null, "areaName": "阳谷县人大代表", "topUserRole": "county", "hasInStation": 1 }
        ],
        featuredProduct: {
          id: '1',
          title: '标题标题',
          name: '测试'
        },
        active: 0,
        searchKeyword: '',
        // 民情民意
        publicOpinionData: [
          { "id": "1983098818371018753", "stationId": "阳谷县狮子楼街道顺发社区人大代表工作室", "receiverId": "", "receiverMobile": "", "recorder": "fa^fa", "recorderMobile": "", "receiverType": 1, "stationLetterType": { "value": "jy", "label": "教育", "name": "教育", "dictCode": "station_letter_type" }, "stationLetterClass": { "value": null, "label": null, "name": null, "dictCode": null }, "title": "测试1111", "content": "1", "pictures": "", "attachmentIds": null, "receiveTime": 1761642577737, "senderId": "o9RxP17C9wbKskQAc9DvetrOpYhY", "senderUserName": "旺旺", "senderMobile": "", "senderEmail": null, "senderAddr": "", "senderType": null, "hasAnswer": 0, "answerTime": null, "stationAssignType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasComplet": 0, "hasEvaluate": null, "evaluateTime": null, "checkStatus": 1, "createBy": null, "createDate": 1761642577152, "terminalName": null, "businessId": null, "businessCode": null, "city": null, "country": null, "town": null, "letterStatus": "未回复", "evaluateStatus": 1, "myEvaluateStatus": 1, "showCheckStatus": 1 },
          { "id": "1983084242493530114", "stationId": "阳谷县狮子楼街道顺发社区人大代表工作室", "receiverId": "", "receiverMobile": "", "recorder": "1111", "recorderMobile": "", "receiverType": 1, "stationLetterType": { "value": "cjnl", "label": "财经农林", "name": "财经农林", "dictCode": "station_letter_type" }, "stationLetterClass": { "value": null, "label": null, "name": null, "dictCode": null }, "title": "测试", "content": "1111111", "pictures": "", "attachmentIds": null, "receiveTime": 1761639101666, "senderId": "o9RxP12L0elS0Vn-6LSiWnXIRQk8", "senderUserName": "付", "senderMobile": "", "senderEmail": null, "senderAddr": "", "senderType": null, "hasAnswer": 0, "answerTime": null, "stationAssignType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasComplet": 0, "hasEvaluate": null, "evaluateTime": null, "checkStatus": 1, "createBy": null, "createDate": 1761639101992, "terminalName": null, "businessId": null, "businessCode": null, "city": null, "country": null, "town": null, "letterStatus": "未回复", "evaluateStatus": 1, "myEvaluateStatus": 1, "showCheckStatus": 1 }
        ],
        // 信息发布
        infomationCategoryColumn: [
          { "id": "1983099327161065473", "name": "普法1", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          { "id": "1983099327161065472", "name": "普法2", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] }
        ],
        infoList: [
          { "id": "1983099367132782594", "stationId": "1874029185060597762", "columnId": "普法", "infoTitle": "这是信息发布**********6666666666", "infoSubtitle": null, "stationShowCode": { "value": "TUWEN", "label": "图文", "name": "图文", "dictCode": "station_show_code" }, "infoSource": null, "pubTime": 1761642699000, "infoVideo": "", "infoVideoType": "1", "infoPic": "", "infoPicType": "2", "contentType": 1, "stationContentType": { "value": null, "label": null, "name": null, "dictCode": null }, "infoContent": "<p><span style=\"font-family: 微软雅黑; font-size: 12pt;\">这是信息发布**********1</span></p>", "linkUrl": "", "isTop": 0, "isShare": 0, "readCount": 2, "attachmentIds": "", "sort": null, "createBy": "Admin", "createDate": 1761642707987, "businessCode": "news", "stationName": "阳谷县狮子楼街道顺发社区人大代表工作室" }
        ],
        // 站点活动
        activityCategoryColumn: [
          { "id": "1983099327161065473", "name": "普法1", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          { "id": "1983099327161065472", "name": "普法2", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          { "id": "1983099327161065471", "name": "普法3", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          { "id": "1983099327161065470", "name": "普法4", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          { "id": "1983099327161065469", "name": "普法5", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          { "id": "1983099327161065468", "name": "普法6", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] }
        ],
        activityList: [
          { "id": "1983795482446561281", "stationId": "1874029185060597762", "title": "测试活动主题", "titlePic": null, "stationActivityType": { "value": "kchd", "label": "考察活动", "name": "考察活动", "dictCode": "station_activity_type" }, "stationActivityClass": { "value": null, "label": null, "name": null, "dictCode": null }, "address": "", "beginTime": 1761753600000, "endTime": 1761840000000, "stationContentType": { "value": null, "label": null, "name": null, "dictCode": null }, "content": "<p><span style=\"font-family: 微软雅黑; font-size: 12pt;\">测试</span></p>", "personCount": 2, "attachmentIds": "", "isTop": 0, "sort": 0, "needTextMessageNotice": 0, "currentMessageTemplate": "", "isSendbox": 1, "isSendtext": 0, "createBy": "Admin", "createDate": 1761808674810, "stationName": "阳谷县狮子楼街道顺发社区人大代表工作室", "activityStatus": "进行中" }
        ],
        // 国家机关进站
        stateStationCategoryColumn: [
          { "id": "1983099327161065473", "name": "普法1", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          { "id": "1983099327161065472", "name": "普法2", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          { "id": "1983099327161065471", "name": "普法3", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          { "id": "1983099327161065470", "name": "普法4", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          { "id": "1983099327161065469", "name": "普法5", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
        ],
        stateStationList: [
          { "id": "1983099367132782594", "stationId": "1874029185060597762", "columnId": "普法", "infoTitle": "这是信息发布**********6666666666", "infoSubtitle": null, "stationShowCode": { "value": "TUWEN", "label": "图文", "name": "图文", "dictCode": "station_show_code" }, "infoSource": null, "pubTime": 1761642699000, "infoVideo": "", "infoVideoType": "1", "infoPic": "", "infoPicType": "2", "contentType": 1, "stationContentType": { "value": null, "label": null, "name": null, "dictCode": null }, "infoContent": "<p><span style=\"font-family: 微软雅黑; font-size: 12pt;\">这是信息发布**********1</span></p>", "linkUrl": "", "isTop": 0, "isShare": 0, "readCount": 2, "attachmentIds": "", "sort": null, "createBy": "Admin", "createDate": 1761642707987, "businessCode": "news", "stationName": "阳谷县狮子楼街道顺发社区人大代表工作室" }
        ],
        loading: false,
      },
      mounted () {
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        if (id) {
          // this.getStationDetail()
          // this.getRepresentatives()
        }
      },
      methods: {
        // 获取工作站详情
        getStationDetail () {
          this.loading = true
          var interfacecontent = {
            stationId: id
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicAllListKey)
          // 调用通用接口函数
          vaildInterfacefn('dbllztuhek', 'qdrdllzStationDetail', biz_content, '2').then(res => {
            var ret = JSON.parse(SM.decrypt(res, allListKey));
            if (ret.code === 200 && ret.data) {
              this.info = ret.data;
            } else {
              // 使用默认数据
              this.info = {
                name: '阳谷县狮子楼街道顺发社区人大代表工作室',
                contactUserName: '张武',
                contactTelephone: '15566666666',
                openTime: '8:00-18:00',
                address: '阳谷县狮子楼街道顺发社区人大代表工作室'
              }
            }
          })
        },

        // 获取值班信息（实际项目中可以调用这个方法）
        getDutyList () {
          var interfacecontent = {
            stationId: id,
            date: new Date().toISOString().split('T')[0] // 获取今天的日期
          };
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicAllListKey);
          vaildInterfacefn('dbllztuhek', 'qdrdllzDutyList', biz_content, '2').then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, allListKey));
              if (ret.code === 200 && ret.data) {
                this.dutyList = ret.data;
              }
            } catch (error) {
              console.log('解析JSON时发生错误：', error);
            }
          }).catch(err => {
            console.log('请求失败：', err);
          })
        },

        // 获取驻站代表列表
        getRepresentatives () {
          var interfacecontent = {
            stationId: id
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicAllListKey)
          vaildInterfacefn('dbllztuhek', 'qdrdllzRepresentatives', biz_content, '2').then(res => {
            var ret = JSON.parse(SM.decrypt(res, allListKey));
            if (ret.code === 200 && ret.data) {
              this.representatives = ret.data;
            } else {
              this.representatives = [{
                id: '1',
                name: '张武',
                title: '县人大代表',
                avatar: '../img/def_head_img.jpg'
              }]
            }
          })
        },

        // 获取一站一品
        getFeaturedProduct () {
          var interfacecontent = {
            stationId: id
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicAllListKey)
          vaildInterfacefn('dbllztuhek', 'qdrdllzRepresentatives', biz_content, '2').then(res => {
            var ret = JSON.parse(SM.decrypt(res, allListKey));
            if (ret.code === 200 && ret.data) {
              this.representatives = ret.data;
            } else {
              this.representatives = [{
                id: '1',
                name: '张武',
                title: '县人大代表',
                avatar: '../img/def_head_img.jpg'
              }]
            }
          })
        },

        // 跳转到留言页面
        goToMessage () {
          window.location.href = 'leaveMessage.html?stationId=' + (id || this.info.id) + '&showRepresentativeInfo=false';
        },

        // 跳转到给代表留言页面
        goToLeaveMessageToRepresentative (member) {
          const params = new URLSearchParams()
          params.append('stationId', id || this.info.id);
          params.append('showRepresentativeInfo', 'true')
          params.append('representativeId', member.id)
          window.location.href = 'leaveMessage.html?' + params.toString()
        },

        // 打开一站一品更多列表
        openFeaturedMore () {
          window.location.href = 'newsList.html?id=' + id + '&type=featuredProduct'
        },

        // tab切换
        onClickTab (name, title) {
          // if (title == '为民办事') {
          //   this.getMassMessagesList()
          // } else if (title == '工作制度') {
          //   this.getWorkingSystemList()
          // } else {
          //   this.getActivityDynamicList()
          // }
        },

        // tab选择
        selectTab (item, index) {
          // 移除所有active类
          document.querySelectorAll('.info_tab_item').forEach(el => {
            el.classList.remove('active');
          });
          // 为点击的tab添加active类
          event.target.classList.add('active');

          // 这里可以根据选中的item筛选数据
          console.log('选中的tab:', item.name);
          // 实际项目中可以在这里调用API获取对应分类的数据
        },

        // 打开民情民意详情
        openStationLetterDetails (item) {
          window.location.href = 'publicOpinionDetails.html?id=' + item.id
        },

        // 打开信息发布详情
        openInformationReleaseDetails (item) {
          window.location.href = 'newsDetails.html?id=' + item.id
        },

        // 打开站点活动详情
        activityItemClick (item) {
          window.location.href = 'siteActivityDetails.html?id=' + item.id
        },

        // 打开国家机关进站详情
        stateStationItemClick (item) {
          window.location.href = 'newsDetails.html?id=' + item.id
        },
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>

</body>

</html>